<template>
  <div>
    <div class="div-top">
      <span>导入出错管理</span>
    </div>
    <span class="search_label">题干名称：</span>
    <el-input placeholder="请输入题干名称" width="30px" v-model="question" style="width: 200px; margin-left: 5px" clearable />
    <span class="search_label">题目类型：</span>
    <el-select v-model="questionType" placeholder="请选择题目类型" @change="handleCurrentChange(1)" clearable>
      <el-option v-for="item in questionTypes" :key="item.value" :label="item.name" :value="item.value"> </el-option>
    </el-select>
    <span class="search_label">所属专业：</span>
    <el-select v-model="majorIds" multiple placeholder="请选择所属专业" @change="handleCurrentChange(1)" clearable>
      <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
    </el-select>
    <el-button type="primary" @click="handleCurrentChange(1)" style="margin-left: 5px">搜索</el-button>
    <el-button type="success" @click="exportTopics" icon="el-icon-download">{{ multipleSelect && multipleSelect.length > 0 ? '导出所选（' + multipleSelect.length + '）' : '导出题目' }}</el-button>
    <div style="margin: 20px">
      <div style="margin: 10px">
        <el-table :data="topics" border row-key="questionErrorId" @selection-change="selectionChange">
          <el-table-column type="selection" width="55" align="center" :selectable="selectableItem"> </el-table-column>
          <el-table-column prop="question" label="题干" width="320">
            <template slot-scope="scope">
              <span v-if="scope.row.questionType != 'COMPATIBILITY'">
                <span class="editor_box" v-html="scope.row.question"></span>
              </span>
              <div v-else>
                <div class="list_question" v-for="(item, index) in getQuestion(scope.row)">{{ letters[index] }}、<span class="editor_box" v-html="item"></span></div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="questionType" label="题型" align="center">
            <template slot-scope="scope">
              <span v-for="item in questionTypes" v-if="item.value == scope.row.questionType">{{ item.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="difficulty" label="难度" align="center">
            <template slot-scope="scope">
              <span v-for="item in difficultys" v-if="item.value == scope.row.difficulty">{{ item.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="majorName" label="所属专业" align="center"> </el-table-column>
          <el-table-column prop="questionUse" label="所属题库" align="center">
            <template slot-scope="scope">
              <span v-for="item in questionUses" v-if="item.value == scope.row.questionUse">{{ item.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="errorReason" label="错误原因" align="center"> </el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center"> </el-table-column>
          <el-table-column prop="createUserName" label="创建人" align="center"> </el-table-column>
          <el-table-column label="操作" align="center" width="350">
            <template slot-scope="scope">
              <el-button type="warning" @click="openView(scope.row)" size="small">查看</el-button>
              <el-button type="danger" @click="openDelete(scope.row)" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin: 10px; text-align: center">
          <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="pageNum" :page-sizes="[10, 30, 50, 100, 300]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog :title="getType(form)" class="mini_dialog" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="questionView" width="600px">
      <questionview :form="form" :letters="letters" :difficultys="difficultys" v-if="questionView"></questionview>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="questionView = false">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { questionErrorList, selectQuestionErrorDetail, removeQuestionError, questionErrorExport } from '@/api/questionerror.js'
import { selectTeacherById } from '@/api/teacher.js'
import { putProgress } from '@/utils/oss.js'
import questionview from '@/views/question/view/index'
import { questionTypes } from '@/filters'

export default {
  components: {
    questionview
  },
  data() {
    return {
      userinfo: {},
      majors: [],
      majorIds: [],
      questionType: '',
      question: '',
      questionUse: 1,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      topics: [],
      dialogImport: false,
      questionView: false,
      form: {},
      multipleSelect: [],
      questionUses: [
        {
          name: '正式题库',
          value: 0
        },
        {
          name: '非正式题库',
          value: 1
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,

      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    selectableItem(row, index) {
      if (row.questionType == 'COMPATIBILITY' || row.questionType == 'COMPREHENSIVE') {
        return false
      } else {
        return true
      }
    },
    getQuestion(form) {
      if (form.question) {
        try {
          var questionObject = JSON.parse(form.question)
          return Object.values(questionObject)
        } catch (err) {
          return form.question
        }
      } else {
        return form.question
      }
    },
    getType(form) {
      var questionTypes = this.questionTypes
      var questionTypeName = ''
      if (form && form.questionType) {
        questionTypes.map((item) => {
          if (form.questionType == item.value) {
            questionTypeName = item.name
          }
        })
      }
      return questionTypeName || '题目详情'
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (res.data.majors && res.data.majors.length > 0) {
          this.getList()
        }
      })
    },
    research() {
      this.questionType = ''
      this.question = ''
      this.questionUse = '1'
      this.pageNum = 1
      this.getList()
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum
      this.getList()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getList()
    },
    getList() {
      var majorIds = this.majorIds
      if (!majorIds || majorIds.length <= 0) {
        majorIds = this.majors.map((item) => {
          return item.majorId
        })
      }
      var data = {
        majorIds: majorIds,
        questionUse: this.questionUse,
        question: this.question ? this.question : null,
        questionType: this.questionType ? this.questionType : null,
        schoolId: this.userinfo.schoolId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      questionErrorList(data).then(async (res) => {
        this.topics = res.data.list
        this.total = res.data.total
      })
    },
    selectionChange(val) {
      this.multipleSelect = val
    },
    exportTopics() {
      var multipleSelect = this.multipleSelect
      var questionErrorIds = []
      if (multipleSelect && multipleSelect.length > 0) {
        multipleSelect.map((item) => {
          questionErrorIds.push(item.questionErrorId)
        })
      }
      questionErrorExport({
        questionErrorIds: questionErrorIds && questionErrorIds.length > 0 ? questionErrorIds : null,
        majorIds: this.majorIds
      })
    },
    downloadTemplate() {
      var download = document.createElement('a')
      download.href = this.baseurl + 'topicTemplate.xls'
      $('body').append(download)
      download.click()
    },
    openView(item) {
      selectQuestionErrorDetail({
        questionErrorId: item.questionErrorId
      }).then((res) => {
        this.form = Object.assign({}, item, res.data)
        this.questionView = true
      })
    },
    openDelete(item) {
      this.$confirm('此操作将永久删除此试题, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          removeQuestionError({
            questionErrorId: item.questionErrorId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: '删除成功!',
                title: '提示'
              })
              this.getList()
            }
          })
        })
        .catch((err) => {
          console.log(err)
          this.$message({
            type: 'info',
            message: '已取消删除',
            title: '提示'
          })
        })
    }
  }
}
</script>
