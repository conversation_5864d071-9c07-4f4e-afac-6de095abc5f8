<template>
  <div ref="chart" class="chart"></div>
</template>
<script>
import * as echarts from 'echarts'
import resize from '@/components/Charts/mixins/resize'
import { arabicToChinese } from '@/utils'
export default {
  name: '',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  created() {},
  methods: {
    arabicToChinese,
    init(data) {
      const that = this
      const sort = data.map((item, index) => `第${this.arabicToChinese(index + 1)}次`)
      const importantCounts = data.map((item) => item.importantCount)
      const generalCounts = data.map((item) => item.generalCount)
      const invalidCounts = data.map((item) => item.invalidCount)
      const notCollectCounts = data.map((item) => item.notCollectCount)
      this.chart = echarts.init(this.$refs['chart'])
      const option = {
        legend: {
          top: '30px',
          right: '30px'
        },
        title: {
          text: '近10次病史采集统计',
          left: 20,
          top: 35,
          textStyle: {
            color: '#333', // 标题颜色为红色
            fontSize: 20 // 标题字体大小为16像素
          }
        },
        tooltip: {
          trigger: 'axis',
          // 自定义提示框样式
          backgroundColor: 'rgba(0,0,0,0.5)', // 背景颜色
          textStyle: {
            color: '#fff' // 文字颜色
          }
          // showContent: false
        },
        xAxis: {
          type: 'category',
          axisTick: { show: false },
          axisLabel: {
            padding: [10, 0, 0, 0] // 上、右、下、左的边距
          }
        },
        yAxis: {
          gridIndex: 0,
          name: '个',
          nameTextStyle: {
            padding: [0, 30, 10, 0]
          }
        },
        grid: { left: '30%', bottom: '8%', right: '2%', top: '20%' },
        color: ['#0091FF', '#13CABC', '#FFC02C', '#FF8D72'],
        dataset: {
          source: [
            ['product', ...sort],
            ['重要问题', ...importantCounts],
            ['常规问题', ...generalCounts],
            ['无效问题', ...invalidCounts],
            ['未采集', ...notCollectCounts]
          ]
        },

        series: [
          {
            type: 'line',
            smooth: true,
            symbolSize: 15,
            seriesLayoutBy: 'row',
            emphasis: { focus: 'series' }
          },
          {
            type: 'line',
            smooth: true,
            symbolSize: 15,
            seriesLayoutBy: 'row',
            emphasis: { focus: 'series' }
          },
          {
            type: 'line',
            smooth: true,
            symbolSize: 15,
            seriesLayoutBy: 'row',
            emphasis: { focus: 'series' }
          },
          {
            type: 'line',
            smooth: true,
            symbolSize: 15,
            seriesLayoutBy: 'row',
            emphasis: { focus: 'series' }
          },
          {
            type: 'pie',
            id: 'pie',
            radius: '50%',
            center: ['15%', '55%'], // 调整饼图的中心位置到左边
            emphasis: {
              focus: 'self'
            },
            label: {
              formatter: `{b}: {@${sort[0]}}个 ({d}%)\n\n`,
              color: '#999999'
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < that.chart.getWidth() / 2
              const points = params.labelLinePoints
              // Update the end point.
              points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width
              return {
                labelLinePoints: points
              }
            },
            encode: {
              itemName: 'product',
              value: sort[0],
              tooltip: sort[0]
            }
          }
        ]
      }
      this.chart.on('updateAxisPointer', function (event) {
        const xAxisInfo = event.axesInfo[0]
        if (xAxisInfo) {
          const dimension = xAxisInfo.value + 1
          that.chart.setOption({
            series: {
              id: 'pie',
              label: {
                formatter: '{b}: {@[' + dimension + ']}个 ({d}%)\n\n'
              },
              encode: {
                value: dimension,
                tooltip: dimension
              }
            }
          })
        }
      })
      this.chart.setOption(option)
    }
  }
}
</script>
<style scoped lang="scss">
.chart {
  width: 100%;
  height: 100%;
}
</style>
