<template>
  <div class="topicinfo">
    <el-form :model="form" :rules="rules" ref="addForm">
      <el-row :gutter="20">
        <el-col :span="18">
          <el-input type="textarea" :rows="8" placeholder="请输入内容" v-model="pastinfo" style="width: 100%"> </el-input>
        </el-col>
        <el-col :span="6">
          <!-- <el-alert type="error" effect="dark" :closable="false">
            <span slot="title"> 提示：“配伍题”以及“组合题”请在自定义添加中手动输入 </span>
          </el-alert> -->
        </el-col>
        <el-col :span="24" style="text-align: center; padding: 20px 0">
          <el-button type="primary" @click="pasteChange">生成</el-button>
        </el-col>
        <el-col :span="24">
          <el-form-item label="难度：" :label-width="labelwidth" prop="difficulty">
            <el-radio-group v-model="form.difficulty">
              <el-radio :label="item.value" v-for="item in difficultys">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="题型：" :label-width="labelwidth" prop="questionType">
            <el-radio-group v-model="form.questionType" @change="topicChange">
              <el-radio :label="item.value" v-for="item in questionTypes">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <questioninput ref="questioninput" :getScore="getScore" :socorRegular="socorRegular" :letters="letters" :form="form" :questionType="questionType"></questioninput>
          <div style="text-align: center" v-show="form.questionType">
            <el-button @click="beforePasteCheck" type="primary">保存</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import questioninput from '@/views/question/input/index'
import { questionTypes } from '@/filters'

export default {
  components: {
    questioninput
  },
  props: {
    getScore: {
      type: Boolean,
      required: false,
      default: false
    },
    socorRegular: {
      type: Object,
      required: false,
      default: () => ({})
    }
  },
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else {
        return callback()
      }
    }
    return {
      pastinfo: '',
      pasted: false,
      labelwidth: '110px',
      questionType: '',
      questionUses: [
        {
          name: '正式题库',
          value: 0
        },
        {
          name: '非正式题库',
          value: 1
        }
      ],
      addtypes: [
        {
          name: '自定义添加',
          value: '1'
        },
        {
          name: '复制粘贴',
          value: '2'
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,
      form: {
        questionUse: 1,
        difficulty: '',
        questionType: '',
        question: '',
        questionArr: ['', '', '', ''],
        list: []
      },
      rules: {
        questionUse: [
          {
            required: true,
            message: '请选择所属题库'
          }
        ],
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        questionType: [
          {
            required: true,
            message: '请选择题目类型'
          }
        ],
        question: [
          {
            required: true,
            message: '请选择题干'
          }
        ],
        list: [
          {
            required: true,
            message: '请输入题目详情'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    }
  },
  created() {},
  methods: {
    pasteChange() {
      var pastinfo = this.pastinfo.trim()
      if (!this.pastinfo) {
        this.$message({
          type: 'error',
          message: '请先输入内容'
        })
        return
      }
      var pastText = $('<div>' + pastinfo + '</div>').text()
      pastText = pastText
        .replace(/、/g, '')
        .replace(/[\r\n]/g, '')
        .replace(/\ +/g, '')
        .replace(/[ ]/g, '')
      var analysis = ''
      var lastIndex = pastText.lastIndexOf('解析')
      if (lastIndex >= 0) {
        analysis = pastText.substring(lastIndex)
      }
      var question = pastText
      var questionType = ''
      var answer = ''
      var answers = []
      var options = ''
      var optionsArr = []
      var allanswersArr = []
      var allanswers = ''
      var hasAnswerArr = pastText.split('答案')
      if (hasAnswerArr.length > 0) {
        question = hasAnswerArr[0]
        hasAnswerArr.map((item, index) => {
          if (index > 0) {
            var matchAnswer = item.match(/[A-F]/g)
            if (matchAnswer) {
              allanswersArr = allanswersArr.concat(matchAnswer)
            }
            if (item.indexOf('解析') == -1) {
              allanswers += item
            } else {
              allanswers += item.split('解析')[0]
            }
          }
        })
        if (allanswersArr && allanswersArr.length > 0) {
          answers = allanswersArr.filter((item, index, arr) => {
            return allanswersArr.indexOf(item, 0) == index
          })
          answers = answers.sort()
          answer = answers.join('')
        } else {
          answer = allanswers
        }
      }
      var alloptionsArr = question.match(/[A-F]/g)
      if (alloptionsArr && alloptionsArr.length > 0) {
        alloptionsArr = alloptionsArr.filter((item, index, arr) => {
          return alloptionsArr.indexOf(item, 0) == index
        })
        var optionsStr = alloptionsArr[0] + question.split(alloptionsArr[0])[1]
        question = question.split(alloptionsArr[0])[0]
        alloptionsArr.map((item, index) => {
          var optionStr = ''
          if (index < alloptionsArr.length - 1) {
            optionStr = optionsStr.substring(optionsStr.indexOf(item) + 1, optionsStr.indexOf(alloptionsArr[index + 1]))
          } else {
            optionStr = optionsStr.substring(optionsStr.indexOf(item) + 1)
          }
          optionsArr.push(optionStr)
        })
        if (answers.length == 0) {
          answer = ''
          questionType = 'SINGLE'
        }
        if (answers.length == 1) {
          questionType = 'SINGLE'
        }
        if (answers.length > 1) {
          questionType = 'MULTIPLE'
        }
        if (optionsArr.length < answers.length) {
          for (var i = 1; i < answers.length - optionsArr.length; i++) {
            optionsArr.push('')
          }
        }
      } else if (question.indexOf('正确') > 0 && question.indexOf('错误') > 0) {
        var firesIndex = question.indexOf('正确') < question.indexOf('错误') ? question.indexOf('正确') : question.indexOf('错误')
        var otherInfo = question.substring(firesIndex)
        if (!(answers.length == 1 && (answers[0] == 'A' || answers[0] == 'B'))) {
          if (question.indexOf('正确') < question.indexOf('错误')) {
            answer = otherInfo.indexOf('错误') != otherInfo.lastIndexOf('错误') ? 'B' : 'A'
          } else {
            answer = otherInfo.indexOf('正确') != otherInfo.lastIndexOf('正确') ? 'B' : 'A'
          }
          answers = [answer]
        }
        question = question.substring(0, firesIndex)
        questionType = 'JUDGE'
      } else if (question.indexOf('_') > 0) {
        var questionList = question.split('_').filter((item) => {
          return !!item
        })
        if (answers.length == 0 && allanswers != '') {
          answers.push(allanswers)
        }
        if (answers.length < questionList.length - 1) {
          for (var i = 1; i < questionList.length - 1 - answers.length; i++) {
            answers.push('')
          }
        } else {
          answers.splice(questionList.length - 1)
        }
        answer = answers.join(',')
        question = questionList.join('@')
        questionType = 'COMPLETION'
      } else {
        answer = allanswers
        questionType = 'SHORT'
      }

      if (optionsArr && optionsArr.length > 0) {
        var optionsObj = {}
        optionsArr.map((item, index) => {
          optionsObj[this.letters[index]] = item
        })
        options = JSON.stringify(optionsObj)
      }
      this.questionType = ''
      var form = {
        scoreType: 1,
        score: this.socorRegular[questionType],
        questionUse: 0,
        difficulty: 'SIMPLE',
        questionType,
        question,
        questionArr: ['', '', '', ''],
        list: [
          {
            sortState: 0,
            answer,
            answers,
            options,
            optionsArr,
            analysis,
            scoreType: 1,
            score: this.socorRegular[questionType]
          }
        ],
        options,
        optionsArr,
        answer,
        answers,
        analysis
      }
      if (questionType == 'SHORT') {
        this.$message({
          type: 'error',
          message: '组合题中不允许添加简答题！'
        })
      } else {
        this.$set(this, 'form', form)
        this.$nextTick(() => {
          this.questionType = this.form.questionType
        })
      }
    },
    beforePasteCheck() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.$refs.questioninput
            .beforeCheck()
            .then((resdata) => {
              var data = Object.assign({}, this.form, resdata)
              this.$emit('beforePasteCheck', data)
              this.clearCheck()
              this.pastinfo = ''
            })
            .catch((err) => {})
        }
      })
    },
    clearCheck() {
      this.form = Object.assign(
        {},
        {
          questionUse: 1,
          difficulty: '',
          questionType: '',
          question: '',
          questionArr: ['', '', '', ''],
          list: [],
          options: '',
          optionsArr: ['', '', '', ''],
          answer: '',
          answers: [],
          analysis: '',
          scoreType: 1,
          score: 0
        }
      )
      this.questionType = ''
      this.$refs.addForm.resetFields()
      this.$refs.addForm.clearValidate()
    },
    topicChange() {
      this.questionType = ''
      var form = {
        questionUse: this.form.questionUse,
        difficulty: this.form.difficulty,
        questionType: this.form.questionType,
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: '',
        scoreType: 1,
        score: this.socorRegular[this.form.questionType]
      }
      this.$set(this, 'form', form)
      this.$nextTick(() => {
        this.questionType = this.form.questionType
      })
    }
  }
}
</script>
