import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)

/* Layout */
import Layout from '@/layout'
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    flag: ['user','index']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes 无权限路由
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/',
    component: () => import('@/views/Home/index'),
    name: 'Dashboard',
    meta: {
      title: '首页'
    }
  }
]
/**
 * asyncRoutes 角色路由
 */
export const asyncRoutes = [
  {
    path: '/system',
    component: Layout,
    redirect: '/system/major',
    meta: {
      title: '系统管理',
      icon: 'component',
      noCache: true,
      flag: 'system:system',
      moduleType: 1
    },
    children: [
      {
        path: 'major',
        component: () => import('@/views/system/major/index'),
        name: 'major',
        meta: {
          title: '专业管理',
          icon: 'documentation',
          noCache: true,
          flag: 'system:major'
        }
      },
      {
        path: 'school',
        component: () => import('@/views/system/school/index'),
        name: 'school',
        meta: {
          title: '学校管理',
          icon: 'list',
          noCache: true,
          flag: 'system:school'
        }
      },
      {
        path: 'permission',
        component: () => import('@/views/system/permission/index'),
        name: 'permission',
        meta: {
          title: '权限管理',
          icon: 'component',
          noCache: true,
          flag: 'system:permission'
        }
      },
      {
        path: 'role',
        component: () => import('@/views/system/role/index'),
        name: 'role',
        meta: {
          title: '角色管理',
          icon: 'tab',
          noCache: true,
          flag: 'system:role'
        }
      },
      {
        path: 'param',
        component: () => import('@/views/system/param/index'),
        name: 'param',
        meta: {
          title: '参数管理',
          icon: 'component',
          noCache: true,
          flag: 'system:param'
        }
      },
      {
        path: 'logs',
        component: () => import('@/views/system/logs/index'),
        name: 'logs',
        meta: {
          title: '日志管理',
          icon: 'documentation',
          noCache: true,
          flag: 'system:logs'
        }
      },
      {
        path: 'logsmodel',
        component: () => import('@/views/system/logs/model'),
        name: 'logsmodel',
        hidden: true,
        meta: {
          title: '操作日志',
          icon: 'documentation',
          noCache: true
        }
      },
      {
        path: 'caseDevice',
        component: () => import('@/views/system/caseDevice'),
        name: 'caseDevice',
        meta: {
          title: '设备管理',
          icon: 'caseDevice',
          flag: 'system:caseDevice',
          noCache: true
        }
      },
      {
        path: 'caseQuestionType',
        component: () => import('@/views/system/caseQuestionType'),
        name: 'caseQuestionType',
        meta: {
          title: '问诊类型',
          icon: 'caseQuestionType',
          flag: 'system:caseQuestionType',
          noCache: true
        }
      }
    ]
  },

  {
    path: '/user',
    component: Layout,
    redirect: '/user/teacher',
    meta: {
      title: '用户管理',
      icon: 'component',
      noCache: true,
      flag: 'user:user',
      moduleType: 1
    },
    children: [
      {
        path: 'organization',
        component: () => import('@/views/user/organization/index'),
        name: 'organization',
        meta: {
          title: '组织管理',
          icon: 'component',
          noCache: true,
          flag: 'user:organization'
        }
      },
      {
        path: 'clbum',
        component: () => import('@/views/user/clbum/index'),
        name: 'clbum',
        meta: {
          title: '班级管理',
          icon: 'documentation',
          noCache: true,
          flag: 'user:clbum'
        }
      },
      {
        path: 'teacher',
        component: () => import('@/views/user/user/teacher'),
        name: 'teacher',
        meta: {
          title: '教师管理',
          icon: 'user',
          noCache: true,
          flag: 'user:teacher'
        }
      },
      {
        path: 'student',
        component: () => import('@/views/user/user/student'),
        name: 'student',
        meta: {
          title: '学生管理',
          icon: 'list',
          noCache: true,
          flag: 'user:student'
        }
      }
    ]
  },
  {
    path: '/question',
    component: Layout,
    redirect: '/question/questionlist',
    meta: {
      title: '题目管理',
      icon: 'component',
      noCache: true,
      flag: 'question:question',
      moduleType: 6
    },
    children: [
      {
        path: 'questionlist',
        component: () => import('@/views/question/index'),
        name: 'questionlist',
        meta: {
          title: '题目管理',
          icon: 'list',
          noCache: true,
          flag: 'question:questionlist'
        }
      },
      {
        path: 'questionadd',
        component: () => import('@/views/question/add'),
        name: 'questionadd',
        hidden: true,
        meta: {
          title: '编辑题目',
          icon: 'documentation',
          noCache: true,
          flag: 'question:add',
          activeMenu: '/question/questionlist'
        }
      },
      {
        path: 'questionerror',
        component: () => import('@/views/question/error'),
        name: 'questionerror',
        meta: {
          title: '出错题目',
          icon: 'documentation',
          noCache: true,
          flag: 'question:error'
        }
      }
    ]
  },
  {
    path: '/volume',
    component: Layout,
    redirect: '/volume/exam',
    meta: {
      title: '卷库管理',
      icon: 'component',
      noCache: true,
      flag: 'volume:volume',
      moduleType: 5
    },
    children: [
      // {
      //   path: 'menu',
      //   component: () => import('@/views/volume/menu/index'),
      //   name: 'menu',
      //   meta: {
      //     title: '练习管理',
      //     icon: 'list',
      //     noCache: true,
      //     flag: 'volume:menu'
      //   }
      // },
      // {
      //   path: 'menuadd',
      //   component: () => import('@/views/volume/menu/add'),
      //   name: 'menuadd',
      //   hidden: true,
      //   meta: {
      //     title: '编辑练习',
      //     icon: 'documentation',
      //     noCache: true,
      //     flag: 'volume:menuadd'
      //   }
      // },
      // {
      //   path: 'menuinfo',
      //   component: () => import('@/views/volume/menu/info'),
      //   name: 'menuinfo',
      //   hidden: true,
      //   meta: {
      //     title: '查看练习',
      //     icon: 'documentation',
      //     noCache: true,
      //     flag: 'volume:menuinfo'
      //   }
      // },
      // {
      //   path: 'simulation',
      //   component: () => import('@/views/volume/simulation/index'),
      //   name: 'simulation',
      //   meta: {
      //     title: '模拟考试',
      //     icon: 'documentation',
      //     noCache: true,
      //     flag: 'volume:simulation'
      //   }
      // },
      {
        path: 'exam',
        component: () => import('@/views/volume/exam/index'),
        name: 'exam',
        meta: {
          title: '正式考试',
          icon: 'table',
          noCache: true,
          flag: 'volume:exam'
        }
      },
      {
        path: 'examadd',
        component: () => import('@/views/volume/exam/add'),
        name: 'examadd',
        hidden: true,
        meta: {
          title: '编辑考试',
          icon: 'documentation',
          noCache: true,
          flag: 'volume:examadd',
          activeMenu: '/volume/exam'
        }
      },
      {
        path: 'examinfo',
        component: () => import('@/views/volume/exam/info'),
        name: 'examinfo',
        hidden: true,
        meta: {
          title: '查看考试',
          icon: 'documentation',
          noCache: true,
          flag: 'volume:examinfo',
          activeMenu: '/volume/exam'
        }
      },
      {
        path: 'correct',
        component: () => import('@/views/volume/correct/index'),
        name: 'correct',
        meta: {
          title: '理论成绩',
          icon: 'edit',
          noCache: true,
          flag: 'volume:correct'
        }
      },
      {
        path: 'correctlist',
        component: () => import('@/views/volume/correct/list'),
        name: 'correctlist',
        hidden: true,
        meta: {
          title: '批改列表',
          icon: 'edit',
          noCache: true,
          flag: 'volume:correctlist',
          activeMenu: '/volume/correct'
        }
      },
      {
        path: 'correctinfo',
        component: () => import('@/views/volume/correct/info'),
        name: 'correctinfo',
        hidden: true,
        meta: {
          title: '批改详情',
          icon: 'edit',
          noCache: true,
          flag: 'volume:correctinfo',
          activeMenu: '/volume/correct'
        }
      }
    ]
  },
  // 病例库
  {
    path: '/case',
    component: () => import('@/views/case/index'),
    name: 'case',
    meta: {
      title: '病例库',
      flag: 'case:case',
      moduleType: 2
    }
  },
  {
    path: '/case/historyCollect/:id',
    component: () => import('@/views/case/historyCollect/index'),
    name: 'caseHistoryCollect',
    meta: {
      flag: 'case:case',
      moduleType: 2
    }
  },
  // ai创建病例模块
  {
    path: '/case/aiCrate',
    component: () => import('@/views/case/aiCrate'),
    name: 'aiCrate',
    meta: {
      title: 'Ai创建病例',
      flag: 'case:case',
      moduleType: 2
    }
  },
  {
    path: '/case/aiCreateQuestion/:id',
    component: () => import('@/views/case/aiCrate/aiCreateQuestion'),
    name: 'aiCreateQuestion',
    meta: {
      title: 'Ai创建对话',
      flag: 'case:case',
      moduleType: 2
    }
  },
  // 测试对话页面
  {
    path: '/case/testCaseDialogue/:id',
    component: () => import('@/views/case/testDialogue/testCaseDialogue'),
    name: 'TestCaseDialogue',
    meta: {
      title: '测试对话',
      flag: 'case:case',
      moduleType: 2
    }
  },
  // 问答纠错库
  {
    path: '/caseRectify',
    component: () => import('@/views/caseRectify/index'),
    name: 'caseRectify',
    meta: {
      title: '问答纠错库',
      flag: 'caseRectify:caseRectify',
      moduleType: 3
    }
  },
  // 考核管理
  {
    path: '/caseExam',
    component: () => import('@/views/caseExam/index'),
    name: 'caseExam',
    meta: {
      title: '考核管理',
      flag: 'caseExam:caseExam',
      moduleType: 4
    }
  },
  // 考核成绩管理
  {
    path: '/examGrade',
    component: () => import('@/views/examGrade/index'),
    name: 'examGrade',
    meta: {
      title: '病例考核成绩',
      flag: 'examGrade:examGrade',
      moduleType: 8
    }
  },
  {
    path: '/examGrade/:id',
    component: () => import('@/views/examGrade/details'),
    name: 'examGradeDetails',
    meta: {
      title: '病例考核成绩详情',
      flag: 'examGrade:examGrade',
      moduleType: 8
    }
  },
  {
    path: '/examGrade/student/:examStudentId',
    component: () => import('@/views/examGrade/studentDetails'),
    name: 'examGradeDetails',
    meta: {
      title: '学生考核成绩详情',
      flag: 'examGrade:examGrade',
      moduleType: 8
    }
  },
  // 病例训练记录
  {
    path: '/casePractise',
    component: () => import('@/views/casePractise/index'),
    name: 'casePractise',
    meta: {
      title: '病例训练记录',
      flag: 'casePractise:casePractise',
      moduleType: 7
    }
  },
  {
    path: '/casePractise/details/:id/:name',
    component: () => import('@/views/casePractise/details'),
    name: 'casePractiseDetails',
    meta: {
      title: '病例训练记录详情',
      flag: 'casePractise:casePractise',
      moduleType: 7
    }
  },
  {
    path: '/casePractise/details/record/:id/:caseId/:studentId',
    component: () => import('@/views/casePractise/studentDetails'),
    name: 'casePractise_studentDetails',
    meta: {
      title: '病例训练记录_学生详情',
      flag: 'casePractise:casePractise',
      moduleType: 7
    }
  },
  // 404 page must be placed at the end !!!
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({
      y: 0
    }),
    routes: constantRoutes
  })

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
