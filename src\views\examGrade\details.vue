<template>
  <div class="app-container">
    <el-row class="pageTop">
      <el-button class="goBack" type="primary" icon="el-icon-back" @click="goBack">返回</el-button>
      <div class="title">考核成绩详情</div>
    </el-row>
    <div class="pageContent" v-if="examDetailInfo">
      <!-- 顶部考核信息 -->
      <div class="examInfo_box">
        <el-row class="examInfo">
          <span class="exam-name"
            >考试名称：<i>{{ examDetailInfo.name }}</i></span
          >
          <span class="exam-time"
            >考试时间：<i>{{ examDetailInfo.startTime }} 至 {{ examDetailInfo.endTime }}</i></span
          >
          <span class="exam-class"
            >考试班级：<i>{{ examDetailInfo.clbumNames.replace(/,\s*$/, '') }}</i></span
          >
          <span class="exam-limit"
            >考试限时：<i>{{ examDetailInfo.time }}分钟</i></span
          >
          <span class="exam-total-score"
            >总分：<i>{{ examDetailInfo.allScore }}分</i></span
          >
          <span class="exam-pass-score"
            >及格分：<i>{{ examDetailInfo.passScore }}分</i></span
          >
          <span class="exam-case-name"
            >考核病例：<i>{{ examDetailInfo.cases.length }}个</i></span
          >
        </el-row>
        <el-row class="examInfo">
          <span class="exam-total-number"
            >应考人数：<i>{{ examDetailInfo.allNumber }}人</i></span
          >
          <span class="exam-actual-number"
            >实考人数：<i>{{ examDetailInfo.alreadyNumber }}人</i></span
          >
          <span class="exam-pass-number"
            >及格人数：<i>{{ examDetailInfo.passNumber }}人</i></span
          >
        </el-row>
      </div>
      <!-- 统计 -->
      <div class="statistics">
        <el-row type="flex" justify="space-between">
          <div>
            <ChartGpa ref="ChartGpa" />
          </div>
          <div>
            <ChartScore ref="ChartScore" />
          </div>
        </el-row>
        <el-row type="flex" justify="space-between">
          <div>
            <ChartRate ref="ChartRate" />
          </div>
          <div>
            <ChartRange ref="ChartRange" />
          </div>
        </el-row>
      </div>
      <!-- 考核的病例 -->
      <div class="examCaseInfo" v-if="examDetailInfo.cases && examDetailInfo.cases.length > 1">
        <div class="examCaseInfoTitle">考核的病例</div>
        <div class="examCaseItemBox">
          <div class="examCaseItem" v-for="(item, index) in examDetailInfo.cases" :key="item.case_id" @click="lookCaseDetail(item, index)">
            <div class="examCaseItem_header">病例{{ index + 1 }}</div>
            <div class="examCaseItem_body">
              <Avatar :age="item.age" :sex="item.sex" />
              <div class="caseName">
                <div>{{ item.name }}</div>
                <span>{{ item.real_name }}</span>
                <span>{{ item.age }}岁</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 全部考核成绩 -->
      <div class="allExamGrade">
        <div class="allExamGrade_title">全部考核成绩</div>
        <!-- 搜索 -->
        <el-row class="searchRow" type="flex" align="middle" justify="space-between">
          <el-form label-width="90px" inline>
            <el-form-item label="姓名:" label-width="50px">
              <el-input class="studentName" v-model="queryInfo.studentName" size="small" placeholder="考生姓名"></el-input>
            </el-form-item>
            <el-form-item label="选择班级:">
              <el-select class="selectClbum" v-model="queryInfo.clbumId" placeholder="选择班级" @focus="getClbum" @change="getStudentList">
                <el-option v-for="item in clbumList" :key="item.clbumId" :label="item.clbumName" :value="item.clbumId"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否及格:">
              <el-radio-group v-model="queryInfo.isPass" @change="getStudentList">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="提交时间:">
              <el-date-picker v-model="time" size="small" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange"> </el-date-picker>
            </el-form-item>
            <el-form-item label="状态:" label-width="50px">
              <el-select class="selectClbum" v-model="queryInfo.state" placeholder="请选择状态" @change="getStudentList">
                <el-option label="未参考" :value="1"> </el-option>
                <el-option label="考试中" :value="2"> </el-option>
                <el-option label="已结束" :value="3"> </el-option>
                <el-option label="异常" :value="4"> </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div>
            <el-button type="primary" @click="getStudentList">查询</el-button>
            <el-button type="primary" plain @click="reset">重置</el-button>
            <el-button type="primary" plain @click="exportExcel">导出Excel</el-button>
          </div>
        </el-row>
        <el-table :data="studentList" style="width: 100%" header-cell-class-name="tableHeader" cell-class-name="tableCell" @sort-change="sortChange">
          <el-table-column align="center" label="序号" width="80" type="index"> </el-table-column>
          <el-table-column align="center" prop="name" label="学生信息" width="width">
            <template v-slot="{ row }">
              <span>{{ row.name }} ({{ row.sex === 'F' ? '女' : '男' }}) {{ row.loginName }} {{ row.clbumName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="score" label="总得分" width="width" sortable="custom"> </el-table-column>
          <el-table-column align="center" prop="time" label="总用时" width="width">
            <template v-slot="{ row }">
              <span>{{ parseFloat(parseFloat(row.time / 60).toFixed(1)) }}分钟</span>
            </template>
          </el-table-column>
          <!-- 动态列 -->
          <el-table-column v-for="caseKey in caseKeys" :key="caseKey" :prop="caseKey" :label="`病例${caseKey.slice(-1)}得分`" align="center"></el-table-column>
          <el-table-column align="center" prop="state" label="状态" width="width">
            <template v-slot="{ row }">
              <el-tag :type="rowStateStyle(row)">{{ row.state === 1 ? '未参考' : row.state === 2 ? '考试中' : row.state === 3 ? '已结束' : '异常' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" label="是否及格" width="width">
            <template v-slot="{ row }">
              <span>{{ row.isPass ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="提交时间" width="width">
            <template v-slot="{ row }">
              <span v-if="row.endTime">{{ row.endTime | formatDate('yyyy-MM-dd') }}</span>
              <span v-else>---</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="time" label="操作" width="width">
            <template v-slot="{ row }">
              <el-button type="primary" size="small" @click="studentDetails(row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination class="pagination" style="text-align: center; margin-top: 15px" :current-page.sync="queryInfo.pageNum" :page-size.sync="queryInfo.pageSize" background layout="total, prev, pager, next, jumper" :total="total" @size-change="getStudentList" @current-change="getStudentList"> </el-pagination>
      </div>
      <!-- 病例详情 -->
      <CaseDetails ref="CaseDetails" :show-dialog.sync="caseDialog" />
    </div>
  </div>
</template>
<script>
import { caseExamDetail, caseExamExamStudentList, caseExamStudentStatis, caseExamClbumList, caseExamExportStudentList } from '@/api/caseExam'
import { formatDate } from '@/filters'
import ChartGpa from '@/views/examGrade/components/Chart_gpa'
import ChartScore from '@/views/examGrade/components/Chart_Score'
import ChartRate from '@/views/examGrade/components/Chart_Rate'
import ChartRange from '@/views/examGrade/components/Chart_Range'
import Avatar from '@/views/casePractise/components/Avatar'
import CaseDetails from '@/views/examGrade/components/CaseDetails'
export default {
  name: '',
  components: {
    ChartGpa,
    ChartScore,
    ChartRate,
    ChartRange,
    Avatar,
    CaseDetails
  },
  data() {
    return {
      examDetailInfo: null,
      queryInfo: {
        examId: this.$route.params.id,
        studentName: null,
        isPass: null,
        startTime: null,
        endTime: null,
        state: null,
        clbumId: null, // 班级id
        sort: null, // 1 总分数 2 总用时
        orderby: null, // 1 倒叙 2 正序
        pageNum: 1,
        pageSize: 6
      },
      clbumList: [],
      time: null,
      caseDialog: false,
      total: 0,
      studentList: [],
      caseKeys: [] // 存储动态的病例键
    }
  },
  created() {
    this.getExamDetails()
    this.getStudentList()
  },
  methods: {
    goBack() {
      this.$router.push('/examGrade')
    },
    async getExamDetails() {
      const { data } = await caseExamDetail({ id: this.$route.params.id })
      this.examDetailInfo = { ...data }
      this.getCaseExamStudentStatis()
    },
    async getStudentList() {
      const { data } = await caseExamExamStudentList(this.queryInfo)
      this.total = data.total
      if (data.list.length) {
        // 假设每个对象的结构都是一样的，我们只需要检查第一个对象
        const firstItem = data.list[0]
        // 找到所有的病例键，这里假设它们都以"病例"开头
        this.caseKeys = Object.keys(firstItem).filter((key) => key.startsWith('病例'))
      }
      this.studentList = data.list
    },
    async getCaseExamStudentStatis() {
      const { data } = await caseExamStudentStatis({ examId: this.$route.params.id })
      const classAvg = data.classAvg
      var classScore = data.classScore
      var classRate = data.classRate
      var classRange = data.classRange
      this.$nextTick(() => {
        this.$refs['ChartGpa'].init(classAvg)
        this.$refs['ChartScore'].init(classScore)

        this.$refs['ChartRate'].init(classRate)

        this.$refs['ChartRange'].init(classRange)
      })
    },
    async getClbum() {
      const { data } = await caseExamClbumList({ examId: this.$route.params.id })
      this.clbumList = data
    },
    async exportExcel() {
      const { data } = await caseExamExportStudentList(this.queryInfo)
      if (!data.length) return false
      // 生成包含动态病例键的模板
      const headers = this.generateDynamicTemplate(data[0])
      console.log(headers)
      const res = this.formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        // var tHeader = ['考试名称', '考试开始时间', '考试结束时间', '考试限时', '成绩公布时间', '考试人数', '考试病例', '总分', '及格分', '状态', '创建人', '创建时间']
        excel.export_json_to_excel({
          header: Object.keys(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '考生成绩列表' // 非必填
        })
      })
    },
    generateDynamicTemplate(data) {
      // 基础模板
      let template = {
        姓名: 'name',
        性别: 'sex',
        学号: 'login_name',
        班级: 'clbum_name',
        总得分: 'score',
        总用时: 'time'
      }
      // 检查数据中的动态“病例”键
      const dynamicCases = Object.keys(data).filter((key) => key.startsWith('病例'))

      // 将这些动态键添加到模板中
      dynamicCases.forEach((caseKey) => {
        template[caseKey] = caseKey // 例如，'病例1': '病例1'
      })

      // 在动态病例之后添加
      template['状态'] = 'stateName'
      template['是否及格'] = 'isPass'

      return template
    },
    // 处理导出数据格式
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          if (key === '性别') {
            item[headers[key]] = item[headers[key]] === 'M' ? '男' : '女'
          } else if (key === '总用时') {
            item[headers[key]] = item[headers[key]] == 0 ? '0分钟' : parseFloat(item[headers[key]] / 60).toFixed(1) + '分钟'
          } else if (key === '是否及格') {
            item[headers[key]] = item[headers[key]] ? '是' : '否'
          }
          return item[headers[key]]
        })
      })
    },
    sortChange(val) {
      if (val.prop === 'score') {
        this.queryInfo.sort = 1
      } else if (val.prop === 'time') {
        this.queryInfo.sort = 2
      }
      if (val.order) {
        this.queryInfo.orderby = val.order === 'ascending' ? 2 : 1
      } else {
        this.queryInfo.orderby = null
      }
      this.getStudentList()
    },
    rowStateStyle(row) {
      const type = row.state === 1 ? 'info' : row.state === 2 ? '' : row.state === 3 ? 'warning' : 'danger'
      return type
    },
    reset() {
      this.queryInfo = {
        examId: this.$route.params.id,
        studentName: null,
        isPass: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 6
      }
      this.getStudentList()
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getStudentList()
    },
    studentDetails(row) {
      this.$router.push(`/examGrade/student/${row.examStudentId}`)
    },
    lookCaseDetail(item, index) {
      this.caseDialog = true
      this.$refs['CaseDetails'].caseInfo = { ...item, index }
      this.$refs['CaseDetails'].caseId = item.exam_case_id
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  padding: 0 22px;

  .pageTop {
    margin-top: 40px;
    margin-bottom: 28px;
    .goBack {
      position: absolute;
    }
    .title {
      font-size: 25px;
      text-align: center;
    }
  }
  .pageContent {
    width: 100%;
    background: #ffffff;
    padding-bottom: 20px;
    border: 1px solid #c7cbd0;
    .examInfo_box {
      padding: 25px 20px;
      .examInfo {
        display: flex;
        &:last-of-type {
          margin-top: 20px;
        }
        span {
          margin-right: 65px;
          font-size: 16px;
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          color: #000000;
          &::last-of-type {
            margin-right: 0;
          }
          i {
            display: inline-block;
            font-style: normal;
            font-size: 16px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            color: #666666;
          }
        }
        .exam-name,
        .exam-total-number {
          margin-right: 30px;

          i {
            width: 210px;
          }
        }
        .exam-time,
        .exam-actual-number {
          margin-right: 30px;

          i {
            width: 370px;
          }
        }
        .exam-class {
          margin-right: 30px;
          i {
            width: 180px;
          }
        }
        .exam-total-number,
        .exam-actual-number,
        .exam-pass-number {
          i {
            font-size: 16px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: bold;
            color: #1890ff;
          }
        }
      }
    }
    .statistics {
      .el-row {
        border-top: 1px solid #c7cbd0;
        border-bottom: 1px solid #c7cbd0;
        &:last-of-type {
          border-top: none;
        }
        & > div {
          flex: 1;
          height: 443px;
          background: #ffffff;
          &:first-of-type {
            border-right: 1px solid #c7cbd0;
          }
        }
      }
    }
    .examCaseInfo {
      padding: 30px 0 0 20px;
      .examCaseInfoTitle {
        font-size: 20px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        color: #333333;
      }
      .examCaseItemBox {
        display: flex;
        margin-top: 20px;
        .examCaseItem {
          min-width: 261px;
          height: 129px;
          padding-right: 10px;
          margin-right: 16px;
          background: #f4f6f8;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #e0e4e8;
          cursor: pointer;
          .examCaseItem_header {
            padding: 10px 0 10px 20px;
            border-bottom: 1px solid #e0e4e8;
            font-size: 18px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: bold;
            color: #0091ff;
          }
          .examCaseItem_body {
            display: flex;
            align-items: center;
            padding-top: 18px;
            padding-left: 20px;
            .caseName {
              margin-left: 8px;
              & > div {
                margin-bottom: 5px;
                font-size: 20px;
                font-family:
                  PingFang SC,
                  PingFang SC;
                font-weight: 500;
                color: #000000;
              }
              span {
                font-size: 16px;
                font-family:
                  PingFang SC,
                  PingFang SC;
                font-weight: 500;
                color: #666666;
                &:last-of-type {
                  margin-left: 3px;
                }
              }
            }
          }
        }
      }
    }
    .allExamGrade {
      padding: 30px 20px 0 20px;

      .allExamGrade_title {
        margin-bottom: 28px;
        font-size: 20px;
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: bold;
        color: #333333;
      }
      ::v-deep {
        .searchRow {
          .el-form {
            .el-form-item {
              margin-bottom: 0;
            }
          }
          .el-form-item__label {
            font-size: 16px;
            font-family:
              Source Han Sans CN,
              Source Han Sans CN;
            font-weight: 400;
            color: #121212;
          }
          .studentName,
          .selectClbum > .el-input {
            width: 204px;
            height: 40px;
            .el-input__inner {
              width: 100%;
              height: 100%;
              background: #ffffff;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #dcdfe6;
            }
          }
          .el-date-editor {
            width: 330px;
            height: 40px;
            background: #ffffff;
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #dcdfe6;
          }
          .el-range-editor--small .el-range__icon {
            line-height: 33px;
          }
          .el-date-editor .el-range-separator {
            line-height: 33px;
            width: 10%;
          }
          .el-button--medium {
            width: 100px;
            height: 44px;
            border-radius: 8px;
          }
        }
        .el-table {
          border: 1px solid #dfe6ec;
          border-bottom: none;
          margin-top: 17px;
          .tableHeader {
            height: 40px;
            background: #f4f7ff;
            font-size: 16px;
            font-family:
              Source Han Sans CN,
              Source Han Sans CN;
            font-weight: 400;
            color: #333333;
          }
          .tableCell {
            font-size: 16px;
            font-family:
              PingFang SC,
              PingFang SC;
            font-weight: 500;
            color: #6e6f6d;
          }
        }
      }
    }
  }
}

.header {
  width: 100%;
  height: 134px;
  padding-left: 15px;
  padding-right: 15px;
  margin-bottom: 25px;
  background: linear-gradient(238deg, #f9fcff 0%, #bcddff 100%);
  border-radius: 4px 4px 0px 0px;
  .header_left {
    display: flex;
    align-items: center;
    & > div {
      display: flex;
      flex-direction: column;
      .examName {
        font-size: 18px;
        font-family:
          Microsoft YaHei-Bold,
          Microsoft YaHei;
        font-weight: bold;
        color: #1a1a1a;
      }
      & > div:nth-of-type(2) {
        span {
          font-size: 14px;
          font-family:
            Microsoft YaHei-Regular,
            Microsoft YaHei;
          font-weight: 400;
          color: #1a1a1a;
        }
      }
      & > div:last-of-type {
        font-size: 14px;
        font-family:
          Microsoft YaHei-Regular,
          Microsoft YaHei;
        font-weight: 400;
        color: #314d61;
      }
      span {
        margin-right: 20px;
      }
    }
  }
  .header_right {
    display: flex;
    flex-wrap: wrap;
    width: 425px;
    span {
      display: flex;
      align-items: center;
      margin-right: 20px;
      font-size: 16px;
      font-family:
        Microsoft YaHei-Regular,
        Microsoft YaHei;
      font-weight: 400;
      color: #1a1a1a;
      img {
        margin-top: 8px;
      }
      i {
        font-style: normal;
        font-size: 16px;
        font-family:
          Microsoft YaHei-Bold,
          Microsoft YaHei;
        font-weight: bold;
        color: #1a1a1a;
      }
    }
  }
}
</style>
<style lang="scss">
.el-select-dropdown {
  .el-scrollbar__wrap {
    overflow: hidden;
  }
}
</style>
