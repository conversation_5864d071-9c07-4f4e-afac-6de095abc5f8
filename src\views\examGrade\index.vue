<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <el-button class="goBack" type="primary" icon="el-icon-back" @click="goBack">返回</el-button>
        <div class="title">病例考核成绩</div>
      </div>
      <el-form ref="form" :model="queryInfo" label-width="80px" inline>
        <el-form-item label="考试名称:">
          <el-input v-model="queryInfo.name" size="small" maxlength="40" placeholder="请输入考试名称" clearable @keydown.native.enter="getList" @clear="getList"></el-input>
        </el-form-item>
        <el-form-item label="考试时间:">
          <el-date-picker v-model="time" size="small" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange" clearable> </el-date-picker>
        </el-form-item>
        <el-form-item label="状态:">
          <el-radio-group v-model="queryInfo.state" @change="getList">
            <el-radio :label="1">未开考</el-radio>
            <el-radio :label="2">考试中</el-radio>
            <el-radio :label="3">已结束</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="success" size="small" @click="getList">查询</el-button>
          <el-button type="primary" size="small" @click="reset">重置</el-button>
          <el-button type="primary" size="small" icon="el-icon-download" @click="download">Excel导出</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="list" style="width: 100%" border>
        <el-table-column prop="name" label="考试名称" width="width" align="center"> </el-table-column>
        <el-table-column label="考试时间" width="180" align="center">
          <template v-slot="{ row }">
            <div>{{ row.startTime }}</div>
            <div>~</div>
            <div>{{ row.endTime }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="time" label="考试限时" width="width" align="center">
          <template v-slot="{ row }">
            <div>{{ row.time }}分钟</div>
          </template>
        </el-table-column>
        <el-table-column prop="caseName" label="考试病例" width="width" align="center"> </el-table-column>
        <el-table-column label="考试形式" width="width" align="center">
          <template v-slot="{ row }">
            <div>
              <span>{{ row.type | examType }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="cou" label="考试人数" width="width" align="center">
          <template v-slot="{ row }">
            <div>
              <span style="color: #409eff">{{ row.alreadyNumber }}</span> / <span>{{ row.allNumber }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="allScore" label="总分" width="80" align="center"> </el-table-column>
        <el-table-column prop="passScore" label="及格分" width="80" align="center"> </el-table-column>
        <el-table-column prop="passNumber" label="及格人数" width="80" align="center"> </el-table-column>
        <el-table-column prop="state" label="状态" width="100" align="center">
          <template v-slot="{ row }">
            <el-tag :type="rowStateStyle(row)">{{ row.state | examState }}</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="createUserName" label="创建人" width="width" align="center"> </el-table-column> -->
        <!-- <el-table-column prop="createTime" label="创建时间" width="180" align="center"> </el-table-column> -->
        <el-table-column prop="name" label="操作" width="170" align="center">
          <template v-slot="{ row }">
            <el-button v-if="row.state === 3" type="primary" size="small" @click="details(row)">考生成绩</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="width: 100%; margin: 15px; text-align: center">
        <el-pagination background @current-change="getList" @size-change="getList" :current-page.sync="queryInfo.pageNum" :page-sizes="[5, 10, 20, 40]" :page-size.sync="queryInfo.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
    </el-card>
  </div>
</template>
<script>
import { caseExamList, caseExamListExport } from '@/api/caseExam'
import { formatDate } from '@/filters'
export default {
  name: 'CaseExam',
  data() {
    return {
      queryInfo: {
        name: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 7
      },
      time: null,
      list: [],
      total: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    goBack() {
      this.$router.push('/')
    },
    async getList() {
      const { data } = await caseExamList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = formatDate(val[0])
        this.queryInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    reset() {
      this.queryInfo = {
        name: null,
        startTime: null,
        endTime: null,
        state: null,
        pageNum: 1,
        pageSize: 7
      }
      this.getList()
    },
    rowStateStyle(row) {
      const type = row.state === 1 ? 'info' : row.state === 2 ? 'success' : ' '
      return type
    },
    async download() {
      const info = {
        name: this.queryInfo.name,
        startTime: this.queryInfo.startTime,
        endTime: this.queryInfo.endTime,
        state: this.queryInfo.state
      }
      const { data } = await caseExamListExport(info)
      const headers = {
        考试名称: 'name',
        考试开始时间: 'startTime',
        考试结束时间: 'endTime',
        考试限时: 'time',
        考试病例: 'caseName',
        考试形式: 'type',
        考试人数: 'alreadyNumber',
        总人数: 'allNumber',
        总分: 'allScore',
        及格分: 'passScore',
        状态: 'state'
        // 创建人: 'realName',
        // 创建时间: 'createTime'
      }
      const res = this.formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        // var tHeader = ['考试名称', '考试开始时间', '考试结束时间', '考试限时', '成绩公布时间', '考试人数', '考试病例', '总分', '及格分', '状态', '创建人', '创建时间']
        excel.export_json_to_excel({
          header: Object.keys(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '病例考核成绩表' // 非必填
        })
      })
    },
    // 处理导出数据格式
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          if (key === '状态') {
            item[headers[key]] = item[headers[key]] === 1 ? '未开考' : item[headers[key]] === 2 ? '考试中' : '已结束'
          } else if (key === '考试形式') {
            item[headers[key]] = item[headers[key]] === 1 ? '随机全考' : item[headers[key]] === 2 ? '随机抽考一个' : '考一个'
          }
          return item[headers[key]]
        })
      })
    },
    details(row) {
      this.$router.push(`examGrade/${row.examId}`)
      console.log(row)
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  .el-card {
    height: 100%;
    width: 100%;
    .goBack {
      position: absolute;
    }
    .title {
      font-size: 25px;
      text-align: center;
    }
  }
}
</style>
