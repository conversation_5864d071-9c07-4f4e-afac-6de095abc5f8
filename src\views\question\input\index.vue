<template>
  <div v-if="questionType">
    <tsingle :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form" ref="inputmain" v-if="questionType == 'SINGLE'"></tsingle>
    <tmultiple :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form" ref="inputmain" v-if="questionType == 'MULTIPLE'"></tmultiple>
    <tjudge :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form" ref="inputmain" v-if="questionType == 'JUDGE'"></tjudge>
    <tcompletion :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form" ref="inputmain" v-if="questionType == 'COMPLETION'"></tcompletion>
    <tshort :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form" ref="inputmain" v-if="questionType == 'SHORT'"></tshort>
    <tcompatiblity :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form" ref="inputmain" v-if="questionType == 'COMPATIBILITY'"></tcompatiblity>
    <tcomprehensive :letters="letters" :getScore="getScore" :socorRegular="socorRegular" :form="form" ref="inputmain" v-if="questionType == 'COMPREHENSIVE'"></tcomprehensive>
  </div>
</template>

<script>
import tsingle from '@/views/question/input/tsingle'
import tmultiple from '@/views/question/input/tmultiple'
import tjudge from '@/views/question/input/tjudge'
import tcompletion from '@/views/question/input/tcompletion'
import tshort from '@/views/question/input/tshort'
import tcomprehensive from '@/views/question/input/tcomprehensive'
import tcompatiblity from '@/views/question/input/tcompatiblity'
export default {
  components: {
    tsingle,
    tmultiple,
    tjudge,
    tcompletion,
    tshort,
    tcomprehensive,
    tcompatiblity
  },
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({})
    },
    questionType: {
      type: String,
      required: true,
      default: null
    },
    letters: {
      type: Array,
      required: true
    },
    getScore: {
      type: Boolean,
      required: false,
      default: false
    },
    socorRegular: {
      type: Object,
      required: false,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    beforeCheck() {
      return new Promise((resolve, reject) => {
        this.$refs.inputmain
          .beforeSubmit()
          .then((data) => {
            resolve(data)
          })
          .catch(() => {
            reject()
          })
      })
    }
  }
}
</script>
