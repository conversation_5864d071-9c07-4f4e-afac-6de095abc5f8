<template>
  <div class="clearfix">
    <div class="topform_cont clearfix">
      <el-form :model="topform" :rules="rules" ref="topform" class="clearfix">
        <div class="topicinfo_top">
          <el-row>
            <el-col :span="6">
              <el-form-item label="所属专业：" :label-width="labelwidth" prop="majorId">
                <el-select v-model="topform.majorId" placeholder="请选择所属专业" style="width: 207px" @change="clearAll">
                  <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="试卷名称：" :label-width="labelwidth" prop="paperName">
                <el-input v-model="topform.paperName" placeholder="请输入试卷名称" maxlength="20" style="width: 207px"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="试卷时长：" :label-width="labelwidth" prop="duration">
                <el-input v-model="topform.duration" placeholder="请输入时长（分钟）" style="width: 207px"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="及格分数：" :label-width="labelwidth" prop="passScore">
                <el-input v-model="topform.passScore" placeholder="请输入及格分数" style="width: 207px"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-row class="menuinfo_botttom">
          <el-col :span="12">
            <el-form-item label="试题添加：" :label-width="labelwidth" prop="examinePaperQuestionReqs">
              <el-button v-for="item in addtypes" @click="changeType(item)" :type="viewIndex == -1 && addtype == item.value ? 'primary' : ''">
                {{ item.name }}
              </el-button>
              <el-button @click="regettopics" type="primary"> 题库选择 </el-button>
              <el-tooltip v-if="addtype == '1' || addtype == '2'" class="item" effect="dark" content="您在自定义添加界面与复制粘贴界面添加的试题，将会自动存入正式题库中" placement="bottom">
                <div class="tip_btn"><i class="el-icon-question"></i></div>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="menuinfo">
        <el-form :model="form" :rules="rules" ref="addForm">
          <el-row>
            <el-col :span="24" v-if="addtype == '1'">
              <el-form-item label="难度：" :label-width="labelwidth" prop="difficulty">
                <el-radio-group v-model="form.difficulty">
                  <el-radio :label="item.value" v-for="item in difficultys">{{ item.name }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="题型：" :label-width="labelwidth" prop="questionType">
                <el-radio-group v-model="form.questionType" @change="topicChange">
                  <el-radio :label="item.value" v-for="item in questionTypes">{{ item.name }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <div v-if="questionType">
                <questioninput ref="questioninput" :getScore="true" :socorRegular="socorRegular" :letters="letters" :form="form" :questionType="questionType"></questioninput>
              </div>
              <div style="text-align: center">
                <el-button @click="jumpQuestion(-1)" :disabled="!beforeBtn" type="primary" v-if="viewIndex >= 0">上一题</el-button>
                <el-button @click="beforeCheck" type="primary" v-if="form.questionType">保存</el-button>
                <el-button @click="jumpQuestion(1)" :disabled="!afterBtn" type="primary" v-if="viewIndex >= 0">下一题</el-button>
              </div>
            </el-col>
            <el-col :span="24" v-if="addtype == '2'">
              <paste :getScore="true" :socorRegular="socorRegular" @beforePasteCheck="beforePasteCheck"></paste>
            </el-col>
            <el-col :span="24" v-if="addtype == '3'">
              <simulation ref="simulation" :socorRegular="socorRegular" @beforeSelectCheck="beforeSelectCheck" :majorId="topform.majorId"></simulation>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="menulist_cont">
      <div class="menulist_conttop">
        <el-button type="primary" @click="checkAll" :disabled="addLoading">保存试卷</el-button>
      </div>
      <div class="menuinfo_list">
        <div class="menuinfo_top">
          试卷题
          <span>题目总数：{{ topform.examinePaperQuestionReqs.length }}</span>
          <span>总分：{{ topform.totalScore }}；</span>
        </div>
        <div class="menuinfo_info" v-for="questionType in questionTypes" v-if="getArray(questionType.value).length > 0">
          <div class="menuinfo_infoname">{{ questionType.name }}（共{{ getArray(questionType.value).length }}题）</div>
          <div class="menuinfo_infolist clearfix" v-show="getArray(questionType.value).length > 0">
            <div @click.stop="openList(item, index)" v-for="(item, index) in getArray(questionType.value)" class="question_answer" :class="{ doing: viewIndex == index && viewType == questionType.value }">
              <div class="question_delete" @click.stop="deleteList(item, index)"><i class="el-icon-close"></i></div>
              {{ index + 1 }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="选择试题" width="65%" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="tableshow">
      <div style="height: 34px; font-size: 16px; font-weight: bold">当前选择题数：{{ selections.length }}</div>
      <div style="margin-bottom: 10px">
        <!--<span class="search_label">所属专业：</span>
				<el-select v-model="questionMajorId" placeholder="请选择所属专业" @change='handleCurrentChange(1)' clearable>
					<el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId">
					</el-option>
				</el-select>-->
        <span class="search_label">题干名称：</span>
        <el-input placeholder="请输入题干名称" width="30px" v-model="questionSele" @change="handleCurrentChange(1)" style="width: 200px; margin-left: 5px" clearable />
        <span class="search_label">题目类型：</span>
        <el-select v-model="questionSeleType" placeholder="请选择题目类型" @change="handleCurrentChange(1)" clearable>
          <el-option v-for="item in questionTypes" :key="item.value" :label="item.name" :value="item.value"> </el-option>
        </el-select>
        <el-button type="primary" @click="handleCurrentChange(1)" style="margin-left: 5px">搜索</el-button>
      </div>
      <el-table :data="topics" border row-key="questionId" @selection-change="selectionChange" ref="topics">
        <el-table-column type="selection" width="55" align="center"> </el-table-column>
        <el-table-column prop="question" label="题干">
          <template slot-scope="scope">
            <span v-if="scope.row.questionType != 'COMPATIBILITY'">
              <span class="editor_box" v-html="scope.row.question"></span>
            </span>
            <div v-else>
              <div class="list_question" v-for="(item, index) in getQuestion(scope.row)">{{ letters[index] }}、<span class="editor_box" v-html="item"></span></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="questionType" label="题型" align="center">
          <template slot-scope="scope">
            <span v-for="item in questionTypes" v-if="item.value == scope.row.questionType">{{ item.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="difficulty" label="难度" align="center">
          <template slot-scope="scope">
            <span v-for="item in difficultys" v-if="item.value == scope.row.difficulty">{{ item.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="majorName" label="所属专业" align="center"> </el-table-column>
        <el-table-column prop="questionUse" label="所属题库" align="center">
          <template slot-scope="scope">
            <span v-for="item in questionUses" v-if="item.value == scope.row.questionUse">{{ item.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center"> </el-table-column>
        <el-table-column prop="createUserName" label="创建人" align="center"> </el-table-column>
      </el-table>
      <div style="margin: 10px; text-align: center">
        <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="pageNum" :page-sizes="[10, 30, 50, 100, 300]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button :disabled="addLoading" @click="calcelSelection">取 消</el-button>
        <el-button :disabled="addLoading" type="primary" @click="addSelection()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { examinePaperList, saveExaminePaper, updateExaminePaper, removeExaminePaper, selectExaminePaperDetailById, releaseExaminePaper, saveAutoExaminePaper } from '@/api/paper.js'
import { questionList, selectQuestionDetail } from '@/api/question.js'
import { selectSimulateParamList } from '@/api/param.js'
import { selectTeacherById } from '@/api/teacher.js'
import questioninput from '@/views/question/input/index'
import paste from '@/views/question/input/paste'
import simulation from '@/views/volume/exam/simulation'
import { questionTypes } from '@/filters'

export default {
  components: {
    questioninput,
    paste,
    simulation
  },
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else if (value <= 0) {
        return callback(new Error('不能小于或等于0'))
      } else {
        return callback()
      }
    }
    var checkPass = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else if (value > this.topform.totalScore) {
        return callback(new Error('不能超过总分'))
      } else if (value <= 0) {
        return callback(new Error('不能小于或等于0'))
      } else {
        return callback()
      }
    }
    var checkArray = (rule, value, callback) => {
      if (!value || value.length <= 0) {
        return callback(new Error('请添加试题'))
      } else {
        return callback()
      }
    }
    return {
      paperId: this.$route.query.paperId,
      labelwidth: '110px',
      addtype: '1',
      userinfo: {},
      majors: [],
      questionType: null,
      questionUses: [
        {
          name: '正式题库',
          value: 0
        },
        {
          name: '非正式题库',
          value: 1
        }
      ],
      addtypes: [
        {
          name: '自定义添加',
          value: '1'
        },
        {
          name: '复制粘贴',
          value: '2'
        },
        {
          name: '智能组卷',
          value: '3'
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,
      SINGLE: [],
      MULTIPLE: [],
      JUDGE: [],
      COMPLETION: [],
      SHORT: [],
      COMPATIBILITY: [],
      COMPREHENSIVE: [],
      viewIndex: -1,
      viewType: -1,
      beforeBtn: true,
      afterBtn: true,
      topform: {
        paperName: '',
        totalScore: 0,
        smallCount: 0,
        examinePaperQuestionReqs: [],
        duration: '',
        passScore: '',
        majorId: '',
        paperQuestionIds: []
      },
      socorRegular: {},
      form: {
        majorId: '',
        questionUse: 1,
        difficulty: '',
        questionType: '',
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: '',
        scoreType: 1,
        score: 0,
        sortState: '0'
      },
      rules: {
        paperName: [
          {
            required: true,
            message: '请输入试卷名称'
          }
        ],
        duration: [
          {
            validator: checkNumber
          },
          {
            required: true,
            message: '请输入时长'
          }
        ],
        passScore: [
          {
            validator: checkPass
          },
          {
            required: true,
            message: '请输入及格分'
          }
        ],
        examinePaperQuestionReqs: [
          {
            validator: checkArray
          }
        ],
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ],
        questionUse: [
          {
            required: true,
            message: '请选择所属题库'
          }
        ],
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        questionType: [
          {
            required: true,
            message: '请选择题目类型'
          }
        ],
        question: [
          {
            required: true,
            message: '请选择题干'
          }
        ],
        list: [
          {
            required: true,
            message: '请输入题目详情'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
      //试题选择
      questionMajorId: '',
      questionSeleType: '',
      questionSele: '',
      pageNum: 1,
      pageSize: 8,
      total: 0,
      topics: [],
      selections: [],
      selectionIndex: 0,
      tableshow: false,
      addLoading: false,
      emptyScores: []
    }
  },
  mounted() {
    this.getUserInfo()
  },
  methods: {
    clearAll() {
      var questionTypes = this.questionTypes
      questionTypes.map((questionType) => {
        this[questionType.value] = []
      })
      this.sumSelection()
      this.clearCheck()
    },
    getScoreList() {
      var teacherId = this.userinfo.teacherId
      selectSimulateParamList({
        teacherId: teacherId,
        schoolId: this.userinfo.schoolId,
        code: 'score_rule'
      }).then((res) => {
        if (res.data) {
          res.data.map((item) => {
            this.socorRegular[item.code] = item.value
          })
        }
      })
    },
    checkAll() {
      if (this.emptyScores && this.emptyScores.length != 0) {
        this.$message({
          type: 'error',
          message: '还有未输入分数的试题！'
        })
        return
      }
      this.$refs.topform.validate((valid) => {
        if (valid) {
          this.addLoading = true
          var examinePaperSaveReq = {
            schoolId: this.userinfo.schoolId,
            majorId: this.topform.majorId,
            paperName: this.topform.paperName,
            duration: this.topform.duration,
            totalScore: this.topform.totalScore,
            smallCount: this.topform.smallCount,
            passScore: this.topform.passScore,
            questionCount: this.topform.questionCount
          }
          var examinePaperQuestionReqs = this.topform.examinePaperQuestionReqs
          examinePaperQuestionReqs.map((item) => {
            var topicScore = 0
            if (item.list && item.list.length > 0) {
              item.list.map((it) => {
                var score = it.score
                score = Number(score)
                if (!isNaN(score)) {
                  topicScore += score
                }
              })
            }
            item.score = topicScore
            item.paperQuestionAnswerList = item.list
          })
          if (this.paperId) {
            examinePaperSaveReq.paperId = this.paperId
            updateExaminePaper({
              paperEditReq: examinePaperSaveReq,
              paperQuestionReqList: examinePaperQuestionReqs,
              paperQuestionIds: this.topform.paperQuestionIds
            }).then((res) => {
              this.addLoading = false
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.closeCheck()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          } else {
            saveExaminePaper({
              examinePaperSaveReq,
              examinePaperQuestionReqs
            }).then((res) => {
              this.addLoading = false
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.closeCheck()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          }
        }
      })
    },
    getQuestion(form) {
      if (form.question) {
        try {
          var questionObject = JSON.parse(form.question)
          return Object.values(questionObject)
        } catch (err) {
          return form.question
        }
      } else {
        return form.question
      }
    },
    regettopics() {
      if (!this.topform.majorId) {
        this.$message({
          type: 'error',
          message: '请先选择专业！'
        })
        return
      } else {
        this.questionMajorId = this.topform.majorId
      }
      this.questionSeleType = ''
      this.questionSele = ''
      this.pageNum = 1
      this.selections = []
      this.topics = []
      this.gettopics()
      this.tableshow = true
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum
      this.gettopics()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.gettopics()
    },
    gettopics() {
      var questionIds = []
      var questionTypes = this.questionTypes
      questionTypes.map((questionType) => {
        this[questionType.value].map((item) => {
          if (item.questionId) {
            questionIds.push(item.questionId)
          }
        })
      })
      var data = {
        majorId: this.questionMajorId ? this.questionMajorId : null,
        question: this.questionSele ? this.questionSele : null,
        questionType: this.questionSeleType ? this.questionSeleType : null,
        questionIds: questionIds,
        questionUse: 0,
        disabled: 1,
        schoolId: this.userinfo.schoolId,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      questionList(data).then(async (res) => {
        this.topics = res.data.list
        this.total = res.data.total
      })
    },
    selectionChange(val) {
      this.selections = val
    },
    calcelSelection() {
      this.selections = []
      this.tableshow = false
      this.$refs.topics.clearSelection()
    },
    addSelection() {
      var selections = this.selections
      if (selections && selections.length > 0) {
        this.addLoading = true
        this.selectionIndex = 0
        this.getAndAdd()
      } else {
        this.$message({
          type: 'error',
          message: '请选择试题'
        })
      }
    },
    getAndAdd() {
      var selections = this.selections
      var selectionIndex = this.selectionIndex
      var socorRegular = this.socorRegular
      selectQuestionDetail({
        questionId: selections[selectionIndex].questionId
      }).then((res) => {
        var item = Object.assign({}, selections[selectionIndex], res.data)
        item.isNew = true
        item.sortState = item.sortState || '0'
        item.list.map((it) => {
          it.score = socorRegular[item.questionType] || 0
          it.scoreType = 1
        })
        var inSetArr = this[item.questionType]
        var flagList = inSetArr.filter((it) => {
          return it.questionId == item.questionId
        })
        if (flagList.length == 0) {
          this[item.questionType].push(item)
        }
        this.nextAdd()
      })
    },
    nextAdd() {
      if (this.selectionIndex < this.selections.length - 1) {
        this.selectionIndex++
        this.getAndAdd()
      } else {
        this.selections = []
        this.selectionIndex = 0
        this.$refs.topics.clearSelection()
        this.tableshow = false
        this.addLoading = false
        this.sumSelection()
      }
    },
    sumSelection() {
      var totalScore = 0
      var smallCount = 0
      var noempty = 1
      var examinePaperQuestionReqs = []
      var emptyScores = []
      var questionTypes = this.questionTypes
      questionTypes.map((questionType) => {
        this[questionType.value + 'NUM'] = 0
        var paperQuestions = this[questionType.value]
        paperQuestions.map((item, index) => {
          if (item.list && item.list.length > 0) {
            var topicScore = 0
            item.list.map((it) => {
              smallCount++
              var score = it.score
              score = Number(score)
              if (!isNaN(score)) {
                totalScore += score
                topicScore += score
                if (score <= 0) {
                  noempty = -1
                  if (!emptyScores.includes(questionType.value + index)) {
                    emptyScores.push(questionType.value + index)
                  }
                }
              } else {
                noempty = -1
                if (!emptyScores.includes(questionType.value + index)) {
                  emptyScores.push(questionType.value + index)
                }
              }
            })
            this[item.questionType + 'NUM'] += topicScore
          }
          examinePaperQuestionReqs.push(item)
        })
      })
      this.topform.examinePaperQuestionReqs = examinePaperQuestionReqs
      this.topform.questionCount = examinePaperQuestionReqs.length
      this.topform.totalScore = totalScore
      this.topform.smallCount = smallCount
      this.topform.noempty = noempty
      this.emptyScores = emptyScores
    },
    deleteList(item, index) {
      this[item.questionType].splice(index, 1)
      if (index == this.viewIndex && item.questionType == this.viewType) {
        this.clearCheck()
      }
      if (item.paperQuestionId) {
        this.topform.paperQuestionIds.push(item.paperQuestionId)
      }
      this.sumSelection()
    },
    openList(item, index) {
      this.clearCheck()
      this.addtype = '1'
      this.questionType = null
      this.viewIndex = index
      this.viewType = item.questionType
      var questionArr = ['', '', '', '']
      var optionsArr = ['', '', '', '']
      var answers = []
      if (item.questionType == 'COMPATIBILITY') {
        try {
          var questionObject = JSON.parse(item.question)
          questionArr = Object.values(questionObject)
        } catch (err) {}
      }
      if (item.options) {
        try {
          var optionObject = JSON.parse(item.options)
          optionsArr = Object.values(optionObject)
        } catch (err) {}
      }
      if (item.list) {
        item.list.map((it) => {
          it.scoreType = it.scoreType ? it.scoreType : 1
          it.score = it.score ? it.score : 0
        })
      }
      var newitem = JSON.parse(JSON.stringify(item))
      this.form = Object.assign({}, this.form, newitem, {
        questionArr,
        optionsArr,
        answers: item.answer ? item.answer.split('') : [],
        answer: item.answer ? item.answer : '',
        score: 0,
        scoreType: 1,
        sortState: item.sortState || '0'
      })

      this.$nextTick(() => {
        this.questionType = this.form.questionType
      })
    },
    pushList(item) {
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      if (viewType == -1 || (viewType != -1 && item.questionType != viewType)) {
        this[item.questionType].push(item)
        if (viewType != -1) {
          this[viewType].splice(viewIndex, 1)
        }
      } else {
        this[item.questionType][viewIndex] = item
      }
      this.sumSelection()
      this.clearCheck()
    },
    getArray(questionType) {
      return this[questionType]
    },
    jumpQuestion(type) {
      this.beforeBtn = true
      this.afterBtn = true
      var viewIndex = this.viewIndex
      var viewType = this.viewType
      var questionTypes = this.questionTypes
      var typeIndex = ''
      questionTypes.map((item, index) => {
        if (item.value == viewType) {
          typeIndex = index
        }
      })
      if (type < 0) {
        if (viewIndex > 0) {
          viewIndex--
          this.openList(this[viewType][viewIndex], viewIndex)
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = newInfo.theArray.length - 1
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.beforeBtn = false
          }
        }
      }
      if (type > 0) {
        if (viewIndex < this[viewType].length - 1) {
          viewIndex++
          this.openList(this[viewType][viewIndex], viewIndex)
        } else {
          var newInfo = this.getBefore(typeIndex, type)
          if (newInfo && newInfo.hasitem) {
            viewIndex = 0
            typeIndex = newInfo.typeIndex
            this.openList(this[questionTypes[newInfo.typeIndex].value][viewIndex], viewIndex)
          } else {
            this.afterBtn = false
          }
        }
      }
    },
    getBefore(typeIndex, type) {
      var questionTypes = this.questionTypes
      if (type < 0) {
        if (typeIndex <= 0) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex--
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
      if (type > 0) {
        if (typeIndex >= questionTypes.length - 1) {
          return {
            typeIndex,
            hasitem: false,
            theArray: []
          }
        } else {
          typeIndex++
          var theArray = this[questionTypes[typeIndex].value]
          if (theArray && theArray.length > 0) {
            return {
              typeIndex,
              hasitem: true,
              theArray: theArray
            }
          } else {
            return this.getBefore(typeIndex, type)
          }
        }
      }
    },
    beforeCheck() {
      this.theCheck()
        .then((data) => {
          data.isEdited = true
          data.isNew = this.viewIndex == -1
          this.pushList(data)
        })
        .catch((err) => {})
    },
    theCheck() {
      return new Promise((resolve, reject) => {
        this.$refs.addForm.validate((valid) => {
          if (valid) {
            this.$refs.questioninput
              .beforeCheck()
              .then((resdata) => {
                var data = Object.assign({}, this.form, resdata)
                resolve(data)
              })
              .catch((err) => {
                reject()
              })
          } else {
            reject()
          }
        })
      })
    },
    beforePasteCheck(item) {
      var data = Object.assign({}, item, {
        majorId: this.topform.majorId,
        isNew: true
      })
      this.pushList(data)
    },
    beforeSelectCheck(data) {
      this.$confirm('此操作将清空当前已添加的试题, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          saveAutoExaminePaper(data).then((res) => {
            if (res.code == '200') {
              var questionTypes = this.questionTypes
              questionTypes.map((questionType) => {
                this[questionType.value] = []
              })
              var selections = res.data
              selections.map((item) => {
                item.isNew = true
                item.sortState = item.sortState || '0'
                item.list.map((it) => {
                  if (item.questionType == 'SINGLE') {
                    it.score = data.singleScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'MULTIPLE') {
                    it.score = data.multipleScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'JUDGE') {
                    it.score = data.judgeScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'COMPLETION') {
                    it.score = data.completionScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'SHORT') {
                    it.score = data.shortScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'COMPATIBILITY') {
                    it.score = data.compatibilityScore || socorRegular[item.questionType]
                  }
                  if (item.questionType == 'COMPREHENSIVE') {
                    it.score = data.comprehensiveScore || socorRegular[item.questionType]
                  }
                  it.scoreType = 0
                })
                this[item.questionType].push(item)
                this.sumSelection()
              })
              this.$refs.simulation.clearCheck()
              this.$nextTick(() => {
                this.changeType({
                  name: '自定义添加',
                  value: '1'
                })
              })
            }
          })
        })
        .catch(() => {})
    },
    clearCheck() {
      this.form = Object.assign(
        {},
        {
          majorId: this.topform.majorId,
          score: 0,
          scoreType: 1,
          questionUse: 0,
          difficulty: '',
          questionType: '',
          question: '',
          questionArr: ['', '', '', ''],
          list: [],
          options: '',
          optionsArr: ['', '', '', ''],
          answer: '',
          answers: [],
          analysis: '',
          sortState: '0'
        }
      )
      this.viewIndex = -1
      this.viewType = -1
      this.questionType = null
      this.$refs.addForm.resetFields()
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate()
      })
    },
    topicChange() {
      var socorRegular = this.socorRegular
      this.questionType = null
      var form = {
        majorId: this.topform.majorId,
        score: socorRegular[this.form.questionType] || 0,
        scoreType: 1,
        questionUse: this.form.questionUse,
        difficulty: this.form.difficulty,
        questionType: this.form.questionType,
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: '',
        sortState: '0'
      }
      this.$set(this, 'form', form)
      this.$nextTick(() => {
        this.questionType = this.form.questionType
      })
    },
    changeType(item) {
      if (this.addtype != item.value) {
        this.addtype = item.value
      } else {
        if (item.value == '1') {
          this.clearCheck()
        }
      }
    },
    getInfo() {
      selectExaminePaperDetailById({
        paperId: this.paperId
      }).then((res) => {
        this.topform = Object.assign({}, this.topform, res.data)
        var examinePaperQuestionReqs = res.data.paperQuestionDetailDtoList
        examinePaperQuestionReqs.map((item) => {
          item.list = item.paperQuestionAnswerList
          item.isNew = false
          this.pushList(item)
        })
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        this.getScoreList()
        if (this.paperId) {
          this.topform.paperName = this.paperName
          this.getInfo()
        }
      })
    },
    closeCheck() {
      // var view = this.$route
      this.$router.push('/volume/exam')
      // this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
      //   if (this.isActive(view)) {
      //     this.toLastView(visitedViews, view)
      //   }
      // })
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView)
      } else {
        if (view.name === 'Dashboard') {
          this.$router.replace({
            path: '/redirect' + view.fullPath
          })
        } else {
          this.$router.push('/')
        }
      }
    }
  }
}
</script>
