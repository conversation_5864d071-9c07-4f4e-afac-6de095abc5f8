;(function (window) {
  window.config = {
    VUE_APP_BASE_API: 'http://192.168.1.100:8711', //100的接口
    VUE_FILE_BASE_PATH: 'http://192.168.1.100:80/cloudFile/SPTalkMain/',
    VUE_WSS_BASE_API: 'ws://192.168.1.100:8711/ws/', // ws地址

    // VUE_APP_BASE_API: 'http://192.168.1.14:8731', //本地接口
    // VUE_FILE_BASE_PATH: 'http://192.168.1.14:8091/cloudFile/SPTalkMain/',
    // VUE_WSS_BASE_API: 'ws://192.168.1.14:8711/ws/', // ws地址
    // VUE_APP_OSS_API: 'http://yun.51-x.cn/oss',
    // VUE_FILE_OSS_PATH: 'https://zfatt.oss-cn-beijing.aliyuncs.com/',

    VUE_AI_BASE_API: 'http://192.168.1.100:8405',
    VUE_AI_BASE_PATH: 'http://192.168.1.100/',
    VUE_AI_MODEL: 'qwen2.5:14b',
    VUE_AI_CHAT_MODEL: 'OLLAMA',
    // 云平台
    // VUE_APP_BASE_API: 'http://yun.51-x.cn/zf_sp_talk_2.0_njwx', //本地接口
    // VUE_APP_OSS_API: 'http://yun.51-x.cn/oss',
    // VUE_FILE_OSS_PATH: 'https://zfatt.oss-cn-beijing.aliyuncs.com/',
    // VUE_FILE_BASE_PATH: 'http://yun.51-x.cn/cloudFile/SPTalk2.0/',

    VUE_IS_LOCAL: true,

    VUE_TITLE: 'sp开放人机对话系统'
  }
  console.log(window.config)
})(window)
