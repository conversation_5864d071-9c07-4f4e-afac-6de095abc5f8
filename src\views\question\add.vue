<template>
  <div class="topicinfo">
    <el-form :model="form" :rules="rules" ref="addForm">
      <el-row>
        <el-col :span="5">
          <el-form-item label="所属专业：" :label-width="labelwidth" prop="majorId">
            <el-select v-model="form.majorId" placeholder="请选择所属专业" style="width: 207px">
              <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="!questionId">
          <el-form-item label="试题添加：" :label-width="labelwidth">
            <el-button v-for="item in addtypes" @click="changeType(item)" :type="addtype == item.value ? 'primary' : ''">
              {{ item.name }}
            </el-button>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="addtype == '1'">
          <el-form-item label="难度：" :label-width="labelwidth" prop="difficulty">
            <el-radio-group v-model="form.difficulty">
              <el-radio :label="item.value" v-for="item in difficultys">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="题型：" :label-width="labelwidth" prop="questionType">
            <el-radio-group v-model="form.questionType" @change="topicChange">
              <el-radio :label="item.value" v-for="item in questionTypes">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <questioninput ref="questioninput" :letters="letters" :form="form" :questionType="questionType"></questioninput>
          <div style="text-align: center">
            <el-button @click="beforeCheck" type="primary" v-if="form.questionType">保存</el-button>
          </div>
        </el-col>
        <el-col :span="24" v-if="addtype == '2'">
          <paste @beforePasteCheck="beforePasteCheck"></paste>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { selectQuestionDetail, saveQuestion, updateQuestion, removeQuestion, updateState, importQuestion, questionExport } from '@/api/question.js'
import { selectTeacherById } from '@/api/teacher.js'
import questioninput from '@/views/question/input/index'
import paste from '@/views/question/input/paste'
import { questionTypes } from '@/filters'
export default {
  components: {
    questioninput,
    paste
  },
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写数字'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请填写数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else {
        return callback()
      }
    }
    return {
      questionId: this.$route.query.questionId,
      labelwidth: '110px',
      addtype: '1',
      userinfo: {},
      majors: [],
      questionType: '',

      addtypes: [
        {
          name: '自定义添加',
          value: '1'
        },
        {
          name: '复制粘贴',
          value: '2'
        }
      ],
      difficultys: [
        {
          name: '简单',
          value: 'SIMPLE'
        },
        {
          name: '中等',
          value: 'MEDIUM'
        },
        {
          name: '困难',
          value: 'DIFFICULTY'
        }
      ],
      questionTypes,
      form: {
        majorId: '',
        questionUse: 0,
        difficulty: '',
        questionType: '',
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: '',
        scoreType: 1,
        score: 0
      },
      rules: {
        majorId: [
          {
            required: true,
            message: '请选择专业'
          }
        ],
        difficulty: [
          {
            required: true,
            message: '请选择难度'
          }
        ],
        questionType: [
          {
            required: true,
            message: '请选择题目类型'
          }
        ],
        question: [
          {
            required: true,
            message: '请选择题干'
          }
        ],
        list: [
          {
            required: true,
            message: '请输入题目详情'
          }
        ]
      },
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    }
  },
  mounted() {
    this.getUserInfo()
  },
  methods: {
    beforePasteCheck(item) {
      var data = Object.assign({}, item, {
        majorId: this.form.majorId,
        questionUse: this.form.questionUse
      })
      saveQuestion(data).then((res) => {
        if (res.code == '200') {
          this.$message({
            type: 'success',
            message: res.message
          })
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
        }
      })
    },
    beforeCheck() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.$refs.questioninput
            .beforeCheck()
            .then((resdata) => {
              var data = Object.assign({}, this.form, resdata)
              if (this.questionId) {
                updateQuestion(data).then((res) => {
                  if (res.code == '200') {
                    this.$message({
                      type: 'success',
                      message: res.message
                    })
                    this.closeCheck()
                  } else {
                    this.$message({
                      type: 'error',
                      message: res.message
                    })
                  }
                })
              } else {
                saveQuestion(data).then((res) => {
                  if (res.code == '200') {
                    this.$message({
                      type: 'success',
                      message: res.message
                    })
                    this.clearCheck()
                  } else {
                    this.$message({
                      type: 'error',
                      message: res.message
                    })
                  }
                })
              }
            })
            .catch((err) => {
              console.log(err)
            })
        }
      })
    },
    clearCheck() {
      this.form = Object.assign(
        {},
        {
          majorId: this.form.majorId || '',
          questionUse: 0,
          difficulty: '',
          questionType: '',
          question: '',
          questionArr: ['', '', '', ''],
          list: [],
          options: '',
          optionsArr: ['', '', '', ''],
          answer: '',
          answers: [],
          analysis: ''
        }
      )
      this.questionType = ''
      this.$refs.addForm.resetFields()
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate()
      })
    },
    topicChange() {
      this.questionType = ''
      var form = {
        majorId: this.form.majorId,
        questionUse: this.form.questionUse,
        difficulty: this.form.difficulty,
        questionType: this.form.questionType,
        question: '',
        questionArr: ['', '', '', ''],
        list: [],
        options: '',
        optionsArr: ['', '', '', ''],
        answer: '',
        answers: [],
        analysis: ''
      }
      this.$set(this, 'form', form)
      this.$nextTick(() => {
        this.questionType = this.form.questionType
      })
    },
    changeType(item) {
      if (this.addtype != item.value) {
        this.addtype = item.value
        if (item.value == '1') {
          this.clearCheck()
        }
      }
    },
    getInfo() {
      selectQuestionDetail({
        questionId: this.questionId
      }).then((res) => {
        var questionArr = ['', '', '', '']
        if (res.data.questionType == 'COMPATIBILITY') {
          try {
            var questionObject = JSON.parse(form.question)
            questionArr = Object.values(questionObject)
          } catch (err) {}
        }
        this.form = Object.assign({}, this.form, res.data, {
          questionArr
        })
        this.$nextTick(() => {
          this.questionType = this.form.questionType
        })
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.majors = res.data.majors
        if (this.questionId) {
          this.getInfo()
        }
      })
    },
    closeCheck() {
      var view = this.$route
      this.$store.dispatch('tagsView/delView', view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView)
      } else {
        if (view.name === 'Dashboard') {
          this.$router.replace({
            path: '/redirect' + view.fullPath
          })
        } else {
          this.$router.push('/')
        }
      }
    }
  }
}
</script>
