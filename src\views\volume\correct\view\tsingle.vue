<template>
  <div>
    <div class="topic_des">
      难度：<span v-for="item in difficultys" v-if="item.value == form.difficulty">{{ item.name }}</span>
    </div>
    <div class="topic_question editor_box" v-html="form.question"></div>
    <div class="topic_selection" v-for="(item, formindex) in form.list" v-if="formindex == 0">
      <div class="topic_selectionit" v-for="(optionItem, index) in item.optionsArr">
        <span class="work-selectionlabel">{{ letters[index] }}、</span>
        <span class="editor_box" v-html="optionItem"></span>
      </div>
      <div class="topic_answermr topic_answer">学生答案：<span class="editor_box" v-html="item.userAnswer"></span></div>
      <div class="topic_answermr topic_answer">正确答案：<span class="editor_box" v-html="item.answer"></span></div>
      <div class="topic_answermr topic_answer">结果：{{ item.isError == '1' ? '错' : '对' }}</div>
      <div class="topic_answermr topic_answer">得分：{{ item.questionScore }}</div>
      <div class="topic_answermr topic_answer">解析：<span class="editor_box" v-html="item.analysis"></span></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      required: true
    },
    letters: {
      type: Array,
      required: true
    },
    difficultys: {
      type: Array,
      required: true
    }
  },
  data() {
    return {}
  },
  created() {
    this.jsonObject()
  },
  methods: {
    jsonObject() {
      var form = this.form
      if (form.list && form.list.length > 0) {
        form.list.map((item) => {
          if (item.options) {
            var optionObject = JSON.parse(item.options)
            item.optionObject = optionObject
            item.optionsArr = Object.values(optionObject)
          } else {
            item.optionObject = {}
            item.optionsArr = []
          }
        })
      } else {
        form.list = []
      }
    }
  }
}
</script>
