<template>
  <div style="margin: 20px">
    <span class="search_label">试卷名称：</span>
    <el-input placeholder="请输入试卷名称" width="30px" v-model="paperName" style="width: 200px; margin-left: 5px" clearable />
    <!--<span class="search_label">创建人：</span>
		<el-input placeholder="请输入创建人" width="30px" v-model="teacherName" style="width:200px;margin-left: 5px" clearable />-->
    <span class="search_label">所属专业：</span>
    <el-select v-model="majorIds" multiple placeholder="请选择所属专业" @change="currentChange(1)" :collapse-tags="true" clearable>
      <el-option v-for="item in majors" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
    </el-select>
    <el-button type="primary" @click="currentChange(1)" size="medium">查询</el-button>
    <el-button type="primary" @click="openAddInfo" size="medium">添加试卷</el-button>
    <!--<el-button type="success" @click="" icon="el-icon-download">{{multipleSelect&& multipleSelect.length>0?'导出所选（'+multipleSelect.length+'）':'导出试卷'}}</el-button>-->
    <el-button type="warning" @click="openScore" size="medium">默认分值</el-button>
    <div style="margin: 15px">
      <el-table :data="exams" border row-key="paperId" @selection-change="selectionChange">
        <el-table-column type="selection" width="55" align="center"> </el-table-column>
        <el-table-column prop="paperName" align="center" label="试卷名称"> </el-table-column>
        <el-table-column prop="majorName" align="center" label="所属专业"> </el-table-column>
        <el-table-column prop="questionCount" align="center" label="试题数量"> </el-table-column>
        <!-- <el-table-column prop="smallCount" align="center" label="小题数量"> </el-table-column> -->
        <el-table-column prop="totalScore" align="center" label="试卷分数"> </el-table-column>
        <el-table-column prop="passScore" align="center" label="及格分数"> </el-table-column>
        <el-table-column prop="duration" align="center" label="试卷时长（分钟）"> </el-table-column>
        <el-table-column align="center" label="状态">
          <template slot-scope="scope">
            <el-tag type="primary" v-if="scope.row.disabled == '0'">未发布</el-tag>
            <el-tag type="success" v-if="scope.row.disabled == '1'">已发布</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" align="center" label="创建时间"> </el-table-column>
        <el-table-column prop="createUserName" align="center" label="创建人"> </el-table-column>
        <el-table-column label="操作" align="center" width="280">
          <template slot-scope="scope">
            <div v-if="scope.row.disabled == '0'">
              <el-button type="primary" @click="openPublish(scope.row)" size="mini">发布试卷</el-button>
              <el-button type="primary" @click="openEditInfo(scope.row)" size="mini">编辑</el-button>
              <el-button type="danger" @click="openDelete(scope.row)" size="mini">删除</el-button>
            </div>
            <div v-else>
              <el-button type="primary" @click="openViewInfo(scope.row)" size="mini">查看</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin: 10px; text-align: center">
        <el-pagination background @current-change="currentChange" @size-change="sizeChange" :current-page="pageNum" :page-sizes="[10, 30, 50, 100, 300]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
    </div>
    <el-dialog title="发布试卷" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="publishDiolog" width="720px">
      <el-form :model="publishForm" :rules="rules" ref="publishForm" :inline="true" style="width: calc(100% - 20px)">
        <el-form-item label="选择班级" prop="clbumIdsArr" label-width="110px">
          <el-button @click="changeClbum(item)" :type="publishForm.clbumIds.includes(item.id) ? 'primary' : ''" v-for="item in clbums">{{ item.clbumName }}</el-button>
        </el-form-item>
        <el-form-item label="考试时间" prop="startTime" label-width="110px">
          <el-date-picker v-model="publishForm.examStartEnd" @change="examTimeChange" type="datetimerange" range-separator="至" start-placeholder="考试开始日期" end-placeholder="考试结束日期" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
        </el-form-item>
        <el-form-item label="成绩发布" prop="scorePublishTime" label-width="110px">
          <el-date-picker v-model="publishForm.scorePublishTime" @change="publishTimeChange" :picker-options="publishOptions" type="datetime" placeholder="选择成绩发布时间" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="批阅时间" prop="correctStartTime" label-width="110px">
          <el-date-picker v-model="publishForm.correctStartEnd" @change="correctTimeChange" :picker-options="correctOptions" :disabled="!publishForm.endTime" type="datetimerange" range-separator="至" start-placeholder="批阅开始日期" end-placeholder="批阅结束日期" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
        </el-form-item>
   
        <el-form-item label="申诉截止" prop="complainEndTime" label-width="110px">
          <el-date-picker v-model="publishForm.complainEndTime" @change="complainEndTimeChange" :disabled="!publishForm.scorePublishTime" :picker-options="orrigendumOptions" type="datetime" placeholder="选择申诉截止时间" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetPublish">取 消</el-button>
        <el-button type="primary" @click="addPublish" :disabled="dataloading">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="默认分值" width="400px" :close-on-press-escape="false" :close-on-click-modal="false" :append-to-body="true" :visible.sync="scoreDiolog">
      <el-form :model="addform" :rules="scoreFormRules" ref="addform">
        <el-form-item label="单选题" :label-width="'80px'" prop="SINGLE">
          <el-col :span="22">
            <el-input v-model="addform.SINGLE" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="多选题" :label-width="'80px'" prop="MULTIPLE">
          <el-col :span="22">
            <el-input v-model="addform.MULTIPLE" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item>
        <!-- <el-form-item label="判断题" :label-width="'80px'" prop="JUDGE">
          <el-col :span="22">
            <el-input v-model="addform.JUDGE" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="填空题" :label-width="'80px'" prop="COMPLETION">
          <el-col :span="22">
            <el-input v-model="addform.COMPLETION" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="简答题" :label-width="'80px'" prop="SHORT">
          <el-col :span="22">
            <el-input v-model="addform.SHORT" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="配伍题" :label-width="'80px'" prop="COMPATIBILITY">
          <el-col :span="22">
            <el-input v-model="addform.COMPATIBILITY" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item>
        <el-form-item label="组合题" :label-width="'80px'" prop="COMPREHENSIVE">
          <el-col :span="22">
            <el-input v-model="addform.COMPREHENSIVE" placeholder="请输入分值"></el-input>
          </el-col>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="scoreDiolog = false">取 消</el-button>
        <el-button @click="scoreChange" type="primary" :disabled="dataloading">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { selectSimulateParamList, updateSimulateParam, saveSimulateParam } from '@/api/param.js'
import { examinePaperList, saveExaminePaper, updateExaminePaper, removeExaminePaper, selectExaminePaperDetailById, releaseExaminePaper } from '@/api/paper.js'
import { selectTeacherById } from '@/api/teacher.js'
export default {
  data() {
    var checkNumber = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入内容'))
      } else if (isNaN(Number(value))) {
        return callback(new Error('请输入数字'))
      } else if (!Number.isInteger(Number(value))) {
        return callback(new Error('请输入整数'))
      } else {
        return callback()
      }
    }
    return {
      userinfo: {},
      majorIds: [],
      paperName: '',
      teacherName: '',
      pageNum: 1,
      pageSize: 10,
      total: 0,
      exams: [],
      multipleSelect: [],
      clbums: [],
      majors: [],
      publishDiolog: false,
      scoreDiolog: false,
      dataloading: false,
      publishForm: {
        paperId: '',
        clbumIds: [],
        clbumIdsArr: '',
        examStartEnd: [],
        startTime: '',
        endTime: '',
        correctStartEnd: [],
        correctStartTime: '',
        correctEndTime: '',
        scorePublishTime: '',
        complainEndTime: ''
      },
      rules: {
        clbumIdsArr: [
          {
            required: true,
            message: '请选择发布班级'
          }
        ],
        startTime: [
          {
            required: true,
            message: '请选择考试时间'
          }
        ],
        correctStartTime: [
          {
            required: true,
            message: '请选择批改时间'
          }
        ],
        scorePublishTime: [
          {
            required: true,
            message: '请选择成绩发布时间'
          }
        ],
        complainEndTime: [
          {
            required: true,
            message: '请选择申诉截止时间'
          }
        ]
      },
      isEdited: false,
      addform: {
        SINGLE: '',
        MULTIPLE: '',
        JUDGE: '',
        COMPLETION: '',
        SHORT: '',
        COMPATIBILITY: '',
        COMPREHENSIVE: ''
      },
      scoreIds: [],
      scoreFormRules: {
        SINGLE: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        MULTIPLE: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        JUDGE: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        COMPLETION: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        SHORT: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        COMPATIBILITY: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ],
        COMPREHENSIVE: [
          {
            required: true,
            message: '请输入参数值'
          },
          {
            validator: checkNumber
          }
        ]
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  computed: {
    correctOptions() {
      var that = this
      return {
        disabledDate(time) {
          let licenseStart = new Date(that.publishForm.endTime)
          licenseStart.setDate(licenseStart.getDate() + 1)
          return time.getTime() < licenseStart.getTime()
        }
      }
    },
    publishOptions() {
      var that = this
      return {
        disabledDate(time) {
          let licenseStart = new Date(that.publishForm.correctEndTime)
          licenseStart.setDate(licenseStart.getDate() + 1)
          return time.getTime() < licenseStart.getTime()
        }
      }
    },
    orrigendumOptions() {
      var that = this
      return {
        disabledDate(time) {
          let licenseStart = new Date(that.publishForm.scorePublishTime)
          licenseStart.setDate(licenseStart.getDate() + 1)
          return time.getTime() < licenseStart.getTime()
        }
      }
    }
  },
  methods: {
    selectionChange(val) {
      this.multipleSelect = val
    },
    currentChange(pageNum) {
      this.pageNum = pageNum
      this.getGroups()
    },
    sizeChange(pageSize) {
      this.pageSize = pageSize
      this.getGroups()
    },
    getGroups() {
      var data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        paperName: this.paperName,
        majorIds: this.majorIds,
        teacherName: this.teacherName,
        schoolId: this.userinfo.schoolId
      }
      examinePaperList(data).then(async (res) => {
        this.exams = res.data.list
        this.total = res.data.total
      })
    },
    getUserInfo() {
      selectTeacherById({}).then((res) => {
        this.userinfo = res.data
        this.clbums = res.data.clbums
        this.majors = res.data.majors
        this.getGroups()
      })
    },
    openScore() {
      this.getScoreList()
      this.scoreDiolog = true
    },
    scoreChange() {
      this.dataloading = true
      setTimeout((_) => {
        this.dataloading = false
      }, 1000)
      var that = this
      this.$refs.addform.validate((valid) => {
        if (valid) {
          var addform = this.addform
          var paramReqs = []
          for (let i in addform) {
            var data = {
              code: i,
              value: addform[i],
              paramName: addform[i + 'name'],
              paramId: that.isEdited ? addform[i + 'id'] : null,
              schoolId: that.userinfo.schoolId
            }
            if (addform[i + 'name']) {
              paramReqs.push(data)
            }
          }
          if (this.isEdited) {
            updateSimulateParam(paramReqs).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.scoreDiolog = false
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          } else {
            saveSimulateParam({
              paramName: '默认分值_' + that.userinfo.name,
              code: 'score_rule',
              value: 'score_rule',
              parentId: '0',
              schoolId: that.userinfo.schoolId,
              paramReqs: paramReqs
            }).then((res) => {
              if (res.code == '200') {
                this.$message({
                  type: 'success',
                  message: res.message
                })
                this.scoreDiolog = false
              } else {
                this.$message({
                  type: 'error',
                  message: res.message
                })
              }
            })
          }
        }
      })
    },
    getScoreList() {
      this.isEdited = false
      var teacherId = this.userinfo.teacherId
      selectSimulateParamList({
        teacherId: teacherId,
        schoolId: this.userinfo.schoolId,
        code: 'score_rule'
      }).then((res) => {
        if (res.data) {
          res.data.map((item) => {
            this.addform[item.code] = item.value
            this.addform[item.code + 'id'] = item.paramId
            this.addform[item.code + 'name'] = item.paramName
            if (item.teacherId == teacherId) {
              this.isEdited = true
            }
          })
        }
      })
    },
    openPublish(item) {
      this.publishForm.paperId = item.paperId
      this.publishDiolog = true
    },
    changeClbum(item) {
      if (this.publishForm.clbumIds.includes(item.id)) {
        var index = this.publishForm.clbumIds.indexOf(item.id)
        this.publishForm.clbumIds.splice(index, 1)
      } else {
        this.publishForm.clbumIds.push(item.id)
      }
      if (this.publishForm.clbumIds.length > 0) {
        this.publishForm.clbumIdsArr = this.publishForm.clbumIds.join(',')
      } else {
        this.publishForm.clbumIdsArr = ''
      }
    },
    examTimeChange() {
      if (this.publishForm.examStartEnd && this.publishForm.examStartEnd.length > 1) {
        this.publishForm.startTime = this.publishForm.examStartEnd[0]
        this.publishForm.endTime = this.publishForm.examStartEnd[1]
      } else {
        this.publishForm.startTime = ''
        this.publishForm.endTime = ''
      }
      this.publishForm.correctStartEnd = []
      this.correctTimeChange()
    },
    correctTimeChange() {
      if (this.publishForm.correctStartEnd && this.publishForm.correctStartEnd.length > 1) {
        this.publishForm.correctStartTime = this.publishForm.correctStartEnd[0]
        this.publishForm.correctEndTime = this.publishForm.correctStartEnd[1]
      } else {
        this.publishForm.correctStartTime = ''
        this.publishForm.correctEndTime = ''
      }
      this.publishForm.scorePublishTime = ''
      this.publishTimeChange()
    },
    publishTimeChange() {
      if (this.publishForm.correctEndTime && this.publishForm.scorePublishTime) {
        let time = new Date(this.publishForm.scorePublishTime)
        let licenseStart = new Date(this.publishForm.correctEndTime)
        licenseStart.setDate(licenseStart.getDate() + 1)
        if (time.getTime() < licenseStart.getTime()) {
          this.publishForm.scorePublishTime = ''
        }
      }
      this.publishForm.complainEndTime = ''
    },
    complainEndTimeChange() {
      if (this.publishForm.scorePublishTime && this.publishForm.complainEndTime) {
        let time = new Date(this.publishForm.complainEndTime)
        let licenseStart = new Date(this.publishForm.scorePublishTime)
        licenseStart.setDate(licenseStart.getDate() + 1)
        if (time.getTime() < licenseStart.getTime()) {
          this.publishForm.complainEndTime = ''
        }
      }
    },
    addPublish() {
      this.dataloading = true
      setTimeout((_) => {
        this.dataloading = false
      }, 1000)
      this.$refs.publishForm.validate((valid) => {
        if (valid) {
          var nowData = new Date()
          var endTime = new Date(this.publishForm.endTime)
          if (endTime < nowData) {
            this.$message({
              type: 'error',
              message: '考试结束时间在当前时间之前，无法发布！'
            })
            return
          }
          var data = Object.assign({}, this.publishForm)
          releaseExaminePaper(data).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                message: '添加成功',
                type: 'success'
              })
              this.getGroups()
              this.resetPublish()
            } else {
              this.$message({
                message: res.message,
                type: 'error'
              })
            }
          })
        }
      })
    },
    resetPublish() {
      this.publishDiolog = false
      this.publishForm.clbumIds = []
      this.publishForm.examStartEnd = []
      this.publishForm.correctStartEnd = []
      this.$refs.publishForm.resetFields()
      this.$refs.publishForm.clearValidate()
    },
    openViewInfo(item) {
      this.$router.replace({
        name: 'examinfo',
        query: {
          paperId: item.paperId
        }
      })
    },
    openAddInfo() {
      this.$router.replace({
        name: 'examadd'
      })
    },
    openEditInfo(item) {
      this.$router.replace({
        name: 'examadd',
        query: {
          paperId: item.paperId
        }
      })
    },
    openDelete(item) {
      this.$confirm('此操作将永久删除此试卷, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          removeExaminePaper({
            paperId: item.paperId
          }).then(async (res) => {
            if (res.code == '200') {
              this.$message({
                type: 'success',
                message: '删除成功!',
                title: '提示'
              })
              this.getGroups()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
            title: '提示'
          })
        })
    }
  }
}
</script>
